﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class Reset : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "audit");

            migrationBuilder.CreateTable(
                name: "Audit",
                schema: "audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    SupplierId = table.Column<int>(type: "int", nullable: false),
                    ItemNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Received = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AuditDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    Weight = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductionDate = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SupplierCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    GTIN = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IngredientStatement = table.Column<int>(type: "int", nullable: false),
                    Allergens = table.Column<int>(type: "int", nullable: false),
                    LotCodeCompliance = table.Column<int>(type: "int", nullable: false),
                    QualityAttributes = table.Column<int>(type: "int", nullable: false),
                    SensoryAttributes = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Audit", x => x.AuditId);
                    table.ForeignKey(
                        name: "FK_Audit_Product_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Product",
                        principalTable: "Product",
                        principalColumn: "ProductId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Audit_Supplier_SupplierId",
                        column: x => x.SupplierId,
                        principalSchema: "Product",
                        principalTable: "Supplier",
                        principalColumn: "SupplierId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductRiskAssessment",
                schema: "audit",
                columns: table => new
                {
                    ProductRiskAssessmentId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RiskLevel = table.Column<int>(type: "int", nullable: false),
                    AuditMonth = table.Column<int>(type: "int", nullable: false),
                    RequireProteinAudit = table.Column<bool>(type: "bit", nullable: false),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductRiskAssessment", x => x.ProductRiskAssessmentId);
                    table.ForeignKey(
                        name: "FK_ProductRiskAssessment_Product_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Product",
                        principalTable: "Product",
                        principalColumn: "ProductId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProteinAudit",
                schema: "audit",
                columns: table => new
                {
                    ProteinAuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PiecesLeft = table.Column<int>(type: "int", nullable: false),
                    WeightLeft = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    AuditId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProteinAudit", x => x.ProteinAuditId);
                    table.ForeignKey(
                        name: "FK_ProteinAudit_Audit_AuditId",
                        column: x => x.AuditId,
                        principalSchema: "audit",
                        principalTable: "Audit",
                        principalColumn: "AuditId");
                });

            migrationBuilder.CreateTable(
                name: "ProteinPiece",
                schema: "audit",
                columns: table => new
                {
                    ProteinPieceId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Grams = table.Column<int>(type: "int", nullable: false),
                    Length = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ProteinAuditId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProteinPiece", x => x.ProteinPieceId);
                    table.ForeignKey(
                        name: "FK_ProteinPiece_ProteinAudit_ProteinAuditId",
                        column: x => x.ProteinAuditId,
                        principalSchema: "audit",
                        principalTable: "ProteinAudit",
                        principalColumn: "ProteinAuditId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Audit_ProductId",
                schema: "audit",
                table: "Audit",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_SupplierId",
                schema: "audit",
                table: "Audit",
                column: "SupplierId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductRiskAssessment_ProductId",
                schema: "audit",
                table: "ProductRiskAssessment",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_ProteinAudit_AuditId",
                schema: "audit",
                table: "ProteinAudit",
                column: "AuditId");

            migrationBuilder.CreateIndex(
                name: "IX_ProteinPiece_ProteinAuditId",
                schema: "audit",
                table: "ProteinPiece",
                column: "ProteinAuditId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProductRiskAssessment",
                schema: "audit");

            migrationBuilder.DropTable(
                name: "ProteinPiece",
                schema: "audit");

            migrationBuilder.DropTable(
                name: "ProteinAudit",
                schema: "audit");

            migrationBuilder.DropTable(
                name: "Audit",
                schema: "audit");
        }
    }
}

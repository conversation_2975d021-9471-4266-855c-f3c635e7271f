﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models
{
    public class ProteinAuditSpec
    {
        public int ProteinAuditSpecId { get; set; }

        public int ProductId { get; set; }
        public Product? Product { get; set; }

        public int MinWeight { get; set; }
        public int MaxWeight { get; set; }
        public decimal MinLength { get; set; }
        public decimal MaxLength { get; set; }


        public DateTime ActivationDate { get; set; }
        public DateTime DeactivationDate { get; set; }

        public DateTime CreatedOn { get; set; }
        [Required]
        [StringLength(300)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(300)]
        public string? ModifiedBy { get; set; }
    }
}

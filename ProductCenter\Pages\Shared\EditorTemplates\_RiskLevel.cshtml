﻿@using ProductCenter.Constants.Enums
@model ProductCenter.Constants.Enums.RiskLevel
@{
    var riskLevels = Enum.GetValues<RiskLevel>().Select(ef => new { Value = (int)ef, Text = ef.ToString() }).ToList();
}

<script>
    function onRiskLevelChange(e){
        var $auditMonth = $("#AuditMonth").data("kendoDropDownList");
        var $proteinAudit = $("#RequireProteinAudit").kendoCheckBox().data("kendoCheckBox");

        if (e.sender.value() == @(Json.Serialize(Constants.Enums.RiskLevel.Low))) {
            $auditMonth.enable(true);
        }
        else{
            $auditMonth.enable(false);
            $auditMonth.value(0);
        }

        if (e.sender.value() == @(Json.Serialize(Constants.Enums.RiskLevel.High))) {
            $proteinAudit.enable(true);
        }
        else {
            $proteinAudit.enable(false);
        }
    }
</script>

@(Html.Kendo().DropDownListFor(m => m)
    .Events(e => e.Change("onRiskLevelChange"))
    .DataValueField("Value")
    .DataTextField("Text")
    .BindTo(riskLevels)
)
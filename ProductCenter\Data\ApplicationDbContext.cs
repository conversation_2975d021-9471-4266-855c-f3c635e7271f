﻿using Microsoft.EntityFrameworkCore;
using ProductCenter.Models;

namespace ProductCenter.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext()
        {
        }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {

        }

        public virtual DbSet<Audit> Audit => Set<Audit>();
        public virtual DbSet<ProteinAudit> ProteinAudit => Set<ProteinAudit>();
        public virtual DbSet<ProteinPiece> ProteinPiece => Set<ProteinPiece>();
        public virtual DbSet<ProductRiskAssessment> ProductRiskAssessment => Set<ProductRiskAssessment>();
        public virtual DbSet<Product> Product => Set<Product>();
        public virtual DbSet<ProductSupplier> ProductSupplier => Set<ProductSupplier>();
        public virtual DbSet<Supplier> Supplier => Set<Supplier>();
        public virtual DbSet<ProteinAuditSpec> ProteinAuditSpec => Set<ProteinAuditSpec>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("audit");

            modelBuilder.Entity<Product>(entity =>
            {
                entity.ToTable("Product", "Product", t => t.ExcludeFromMigrations());
            });
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.ToTable("Supplier", "Product", t => t.ExcludeFromMigrations());
            });
            modelBuilder.Entity<ProductSupplier>(entity =>
            {
                entity.ToTable("x_ProductToSupplier", "Product", t => t.ExcludeFromMigrations());
            });

            modelBuilder.Entity<ProteinPiece>(entity =>
            {
                entity.HasOne(e=>e.ProteinAudit)
                .WithMany(p=>p.ProteinPieces)
                .HasForeignKey(e=>e.ProteinAuditId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ProteinAudit>(entity =>
            {
                entity.HasOne(e=>e.Audit)
                .WithMany(p=>p.ProteinAudits)
                .HasForeignKey(e=>e.AuditId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ProteinAuditSpec>(entity =>
            {
                entity.HasOne(e=>e.Product)
                .WithMany(p=>p.ProteinAuditSpecs)
                .HasForeignKey(e=>e.ProductId)
                .OnDelete(DeleteBehavior.NoAction);
            });
        }
    }
}

﻿using ProductCenter.Constants.Enums;

namespace ProductCenter.Models
{
    public class ProteinPiece
    {
        public int ProteinPieceId { get; set; }
        public int? Grams { get; set; }
        public SpecCompliance? WeightSpecCompliance { get; set; }
        public decimal? Length { get; set; }
        public SpecCompliance? LengthSpecCompliance { get; set; }
        public int ProteinAuditId { get; set; }
        public ProteinAudit ProteinAudit { get; set; } = new();

    }
}

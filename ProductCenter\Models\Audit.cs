﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models
{
    public class Audit
    {
        public int AuditId { get; set; }
        public int ProductId { get; set; }
        public int SupplierId { get; set; }
        //public AuditType AuditType { get; set; }
        public string ItemNumber { get; set; } = string.Empty;

        public DateTime? Received { get; set; }
        public DateTime? AuditDate { get; set; }

        // Case Weight Check
        //public int Quantity { get; set; }
        public string Weight { get; set; } = string.Empty;

        // Label Check
        public string ProductionDate { get; set; } = string.Empty;
        public string SupplierCode { get; set; } = string.Empty;
        public string GTIN { get; set; } = string.Empty;

        public AuditEval IngredientStatement { get; set; }
        public AuditEval Allergens { get; set; }
        public AuditEval LotCodeCompliance { get; set; }


        // Quality Testing
        public AuditEval QualityAttributes { get; set; }
        public AuditEval SensoryAttributes { get; set; }

        public string? Notes { get; set; }

        public DateTime CreatedOn { get; set; }

        [Required]
        [StringLength(300)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(300)]
        public string? ModifiedBy { get; set; }


        public Product? Product { get; set; }
        public Supplier? Supplier { get; set; }

        public List<ProteinAudit> ProteinAudits { get; set; } = new();  
    }
}

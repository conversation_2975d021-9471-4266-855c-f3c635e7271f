﻿@page
@using ProductCenter.Constants.Enums
@model ProductCenter.Pages.Audits.HistoryModel
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "Audit History";
}
<div class="text-center">
    <h5 class="display-5">Audit History</h5>
    <div>
        @(
            Html.Kendo().Grid<Models.ViewModels.AuditVM>()
                .Name("auditGrid")
                .Columns(columns =>
                {
                    columns.Bound(d => d.AuditId).Hidden();
                    columns.Bound(d => d.ProductId).Hidden();
                    columns.Bound(d => d.Product).Filterable(ftb => ftb.Multi(true).Search(true));
                    columns.Bound(d => d.SupplierId).Hidden();
                    columns.Bound(d => d.Supplier);
                    columns.Bound(d => d.Received).Format("{0:d}").Hidden();
                    columns.Bound(d => d.ProductionDate);
                    columns.Bound(d => d.AuditDate).Format("{0:d}");
                    columns.Bound(d => d.Weight).Hidden();
                    columns.Bound(d => d.SupplierCode).Hidden();
                    columns.Bound(d => d.ItemNumber).Hidden();
                    columns.Bound(d => d.GTIN).Hidden();
                    columns.Bound(d => d.IngredientStatement).Hidden();
                    columns.Bound(d => d.Allergens).Hidden();
                    columns.Bound(d => d.LotCodeCompliance).Hidden();
                    columns.Bound(d => d.QualityAttributes).Hidden();
                    columns.Bound(d => d.SensoryAttributes).Hidden();
                    columns.Bound(d => d.Notes).Hidden();
                    columns.Bound(d => d.CreatedOn).Hidden().Format("{0:d}");
                    columns.Bound(d => d.CreatedBy).Hidden();
                    columns.Bound(d => d.ModifiedOn).Hidden().Format("{0:d}");
                    columns.Bound(d => d.ModifiedBy).Hidden();
                    columns.Command(c => { c.Custom("Details").IconClass("k-i-search").Click("showDetails"); c.Custom("Edit").Click("onAudit").IconClass("k-i-pencil"); c.Destroy(); });
                })
                .Excel(excel => excel.AllPages(true)
                    .FileName("ProductAuditHistory.xlsx")
                    .Filterable(true)
                )
                .ToolBar(t => { t.Search(); t.Excel(); })
                .Search(s => s.Field(m => m.Product))
                .Resizable(r=>r.Columns(true))
                .Filterable()
                .ColumnMenu(true)
                .Size(ComponentSize.Small)
                .Sortable()
                .Scrollable(s => s.Virtual(true))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Read(r => r.Url("/Audits/History?handler=ReadAudits").Data("forgeryToken"))
                .Destroy(r => r.Url("/Audits/History?handler=DestroyAudit").Data("forgeryToken"))
                .Sort(s => { s.Add("AuditDate").Descending(); })
                .Model(m =>
                {
                    m.Id(i=>i.AuditId);
                })
            )
            )
    </div>
</div>

@(Html.Kendo().Window().Name("Details")
    .Title("Audit Details")
    .Visible(false)
    .Modal(true)
    .Scrollable(true)
    .Draggable(true)
    .Width(500)
    .Height(600)
)

@section Scripts {
    <script type="text/x-kendo-template" id="template">
        <div id="details-container">
            <h6>#= Product #</h6>
            <em>#= Supplier #</em>
            <hr/>
            <dl>
                <dt>Item Number: </dt><dl>#= ItemNumber #</dl>
                <dt>Received: </dt><dl>#= kendo.toString(Received, 'd') #</dl>
                <dt>Audit Date: </dt><dl>#= kendo.toString(AuditDate, 'd') #</dl>
                <dt>Weight: </dt><dl>#= Weight #</dl>
                <dt>Production Date: </dt><dl>#= ProductionDate #</dl>
                <dt>Supplier Code: </dt><dl>#= SupplierCode #</dl>
                <dt>GTIN: </dt><dl>#= GTIN #</dl>
                <dt>Ingredient Statement: </dt><dl>#= getEnumValue(IngredientStatement) #</dl>
                <dt>Allergens: </dt><dl>#= getEnumValue(Allergens) #</dl>
                <dt>GSI-121 Compliance: </dt><dl>#= getEnumValue(LotCodeCompliance) #</dl>
                <dt>Quality Attributes: </dt><dl>#= getEnumValue(QualityAttributes) #</dl>
                <dt>Sensory Attributes: </dt><dl>#= getEnumValue(SensoryAttributes) #</dl>
                <dt>Comments: </dt><dl>#= Notes #</dl>
                <dt>Created On: </dt><dl>#= kendo.toString(CreatedOn, 'd') #</dl>
                <dt>Created By: </dt><dl>#= CreatedBy #</dl>
                <dt>Modified On: </dt><dl>#= kendo.toString(ModifiedOn, 'd') ?? '' #</dl>
                <dt>Modified By: </dt><dl>#= ModifiedBy ?? '' #</dl>
            </dl>
        </div>
    </script>
    <script type="text/javascript">
        function forgeryToken() {
            return kendo.antiForgeryTokens();
        }

        function showDetails(e) {
            e.preventDefault();

            var detailsTemplate = kendo.template($("#template").html());
            var dataItem = this.dataItem($(e.currentTarget).closest("tr"));
            var wnd = $("#Details").data("kendoWindow");

            wnd.content(detailsTemplate(dataItem));
            wnd.center().open();
        }

        function onAudit(e) {
            e.preventDefault();

            var dataItem = this.dataItem($(e.currentTarget).closest("tr"));

            var url = `/Audits/Edit?id=${dataItem.AuditId}`;
            window.location.href = url;
        }

        var auditEvalList = @Json.Serialize(Enum.GetNames(typeof (AuditEval)));
        function getEnumValue(e) {
            return auditEvalList[e];
        }
    </script>
}
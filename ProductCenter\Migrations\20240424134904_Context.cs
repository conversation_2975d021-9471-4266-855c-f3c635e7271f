﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class Context : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProteinAudit_Audit_AuditId",
                schema: "audit",
                table: "ProteinAudit");

            migrationBuilder.AlterColumn<int>(
                name: "AuditId",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ProteinAudit_Audit_AuditId",
                schema: "audit",
                table: "ProteinAudit",
                column: "AuditId",
                principalSchema: "audit",
                principalTable: "Audit",
                principalColumn: "AuditId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProteinAudit_Audit_AuditId",
                schema: "audit",
                table: "ProteinAudit");

            migrationBuilder.AlterColumn<int>(
                name: "AuditId",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_ProteinAudit_Audit_AuditId",
                schema: "audit",
                table: "ProteinAudit",
                column: "AuditId",
                principalSchema: "audit",
                principalTable: "Audit",
                principalColumn: "AuditId");
        }
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class Specs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LengthSpecCompliance",
                schema: "audit",
                table: "ProteinPiece",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WeightSpecCompliance",
                schema: "audit",
                table: "ProteinPiece",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ProteinAuditSpec",
                schema: "audit",
                columns: table => new
                {
                    ProteinAuditSpecId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    MinWeight = table.Column<int>(type: "int", nullable: false),
                    MaxWeight = table.Column<int>(type: "int", nullable: false),
                    MinLength = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MaxLength = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ActivationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeactivationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProteinAuditSpec", x => x.ProteinAuditSpecId);
                    table.ForeignKey(
                        name: "FK_ProteinAuditSpec_Product_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Product",
                        principalTable: "Product",
                        principalColumn: "ProductId");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ProteinAuditSpec_ProductId",
                schema: "audit",
                table: "ProteinAuditSpec",
                column: "ProductId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProteinAuditSpec",
                schema: "audit");

            migrationBuilder.DropColumn(
                name: "LengthSpecCompliance",
                schema: "audit",
                table: "ProteinPiece");

            migrationBuilder.DropColumn(
                name: "WeightSpecCompliance",
                schema: "audit",
                table: "ProteinPiece");
        }
    }
}

using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using ProductCenter.Data;
using ProductCenter.Models.ViewModels;
using ProductCenter.Constants.Enums;
using ProductCenter.Models;
using LogWriterCore;
using ProductCenter.Services;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

namespace ProductCenter.Pages.Audits
{
    public class IndexModel(ApplicationDbContext context, ILogging logging) : PageModel
    {
        public DateTime AnnualDueDate { get; set; } = new DateTime(DateTime.Now.Year + 1, 1, 1);

        public void OnGet()
        {

        }

        public async Task<JsonResult> OnPostReadClearanceAuditsAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                var riskAssessments = await context.ProductRiskAssessment
                    .Include(p => p.Product)
                    .Where(p=>p.RiskLevel == RiskLevel.High)
                    .Select(s => new ProductRiskAssessmentVM
                    {
                        ProductRiskAssessmentId = s.ProductRiskAssessmentId,
                        Product = s.Product.Name,
                        ItemNumber = s.Product.ItemNumber,
                        RequireProteinAudit = s.RequireProteinAudit,
                        RiskLevel = s.RiskLevel,
                        ProductId = s.ProductId
                    })
                    .ToListAsync();

                var audits = await context.Audit.Include(a => a.Supplier).Include(a => a.Product).AsNoTracking().ToListAsync();
                foreach (var riskAssessment in riskAssessments)
                {
                    var lastAudit = audits.Where(a => a.ProductId == riskAssessment.ProductId).OrderByDescending(a => a.AuditDate).FirstOrDefault();
                    riskAssessment.LastAuditDate = lastAudit?.AuditDate;
                    riskAssessment.ProductionDate = lastAudit?.ProductionDate;

                    if (lastAudit != null)
                    {
                        riskAssessment.LastAudit = new AuditVM(lastAudit);
                    }
                }

                return new JsonResult(riskAssessments.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Index.OnPostReadClearanceAuditsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
        public async Task<JsonResult> OnPostReadQuarterlyAuditsAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                var audits = await context.Audit.Include(a => a.Supplier).Include(a => a.Product).AsNoTracking().ToListAsync();

                var riskAssessments =  context.ProductRiskAssessment
                    .Include(p => p.Product)
                    .Where(p=>p.RiskLevel == RiskLevel.Medium)
                    .Select(s => new ProductRiskAssessmentVM
                    {
                        ProductRiskAssessmentId = s.ProductRiskAssessmentId,
                        Product = s.Product.Name,
                        ItemNumber = s.Product.ItemNumber,
                        RiskLevel = s.RiskLevel,
                        AuditMonth = s.AuditMonth,
                        ProductId = s.ProductId
                    })
                    .ToList();

                var qtrRiskAssessments = new List<ProductRiskAssessmentVM>();

                for (int i = 1; i <= 4; i++)
                {
                    DateTime date = DateTime.UtcNow;
                    int quarterNumber = i;
                    DateTime qtrBegin = new DateTime(date.Year, (quarterNumber - 1) * 3 + 1, 1);
                    DateTime qtrEnd = qtrBegin.AddMonths(3);

                    foreach (ProductRiskAssessmentVM riskAssessment in riskAssessments)
                    {
                        ProductRiskAssessmentVM qtrRiskAssessment = new()
                        {
                            ItemNumber = riskAssessment.ItemNumber,
                            Product = riskAssessment.Product,
                            ProductId = riskAssessment.ProductId,
                            ProductRiskAssessmentId = riskAssessment.ProductRiskAssessmentId,
                            AuditQuarter = i
                        };

                        var lastAudit = audits
                            .Where(a => a.ProductId == riskAssessment.ProductId)
                            .Where(a => a.AuditDate >= qtrBegin && a.AuditDate < qtrEnd)
                            .OrderByDescending(a => a.AuditDate)
                            .FirstOrDefault();

                        qtrRiskAssessment.LastAuditDate = lastAudit?.AuditDate;
                        qtrRiskAssessment.ProductionDate = lastAudit?.ProductionDate;

                        if (lastAudit != null)
                        {
                            qtrRiskAssessment.LastAudit = new AuditVM(lastAudit);
                        }

                        qtrRiskAssessments.Add(qtrRiskAssessment);
                    }
                }

                return new JsonResult(qtrRiskAssessments.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Index.OnPostReadQuarterlyAuditsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
        public async Task<JsonResult> OnPostReadAnnualAuditsAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                int year = DateTime.UtcNow.Year;
                DateTime yearBegin = new DateTime(year, 1, 1);
                DateTime yearEnd = new DateTime(year + 1, 1, 1);

                var audits = await context.Audit.Include(a=>a.Supplier).Include(a=>a.Product).AsNoTracking().ToListAsync();

                var riskAssessments = context.ProductRiskAssessment
                    .Include(p => p.Product)
                    .Where(p => p.RiskLevel == RiskLevel.Low)
                    .Select(s => new ProductRiskAssessmentVM
                    {
                        ProductRiskAssessmentId = s.ProductRiskAssessmentId,
                        Product = s.Product.Name,
                        ItemNumber = s.Product.ItemNumber,
                        RiskLevel = s.RiskLevel,
                        AuditMonth = s.AuditMonth,
                        ProductId = s.ProductId
                    })
                    .ToList();

                foreach(var riskAssessment in riskAssessments)
                {
                    var lastAudit = audits.Where(a => a.ProductId == riskAssessment.ProductId).OrderByDescending(a => a.AuditDate).FirstOrDefault();
                    riskAssessment.LastAuditDate = lastAudit?.AuditDate;
                    riskAssessment.ProductionDate = lastAudit?.ProductionDate;

                    if(lastAudit != null)
                    {
                        riskAssessment.LastAudit = new AuditVM(lastAudit);
                    }                    

                    if(riskAssessment.LastAuditDate == null && (int)riskAssessment.AuditMonth < DateTime.Now.Month)
                    {
                        riskAssessment.IsPastDue = 1;
                    }
                }

                return new JsonResult(riskAssessments.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Index.OnPostReadAnnualAuditsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
    }
}

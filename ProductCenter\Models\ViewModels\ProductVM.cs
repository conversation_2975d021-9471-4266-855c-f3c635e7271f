﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models.ViewModels
{
    public class ProductVM
    {
        public int ProductId { get; set; }
        public string? ItemNumber { get; set; }
        public string Name { get; set; } = string.Empty;

        [UIHint("_RiskLevel")]
        public RiskLevel RiskLevel { get; set; }

        [UIHint("_AuditMonthEditor")]
        public AuditMonth AuditMonth { get; set; }
        public bool RequireProteinAudit { get; set; }
    }
}

﻿namespace ProductCenter.Constants.Enums
{
    public enum AuditType
    {
        Routine = 1,
        Clearnace = 2
    }

    public enum AuditEval
    {
        NotEvaluated = 0,
        Pass = 1,
        Fail = 2,
        NA = 3
    }

    public enum AuditFrequency
    {
        None = 0,
        Annual = 1,
        Biannual = 2,
        Triannual = 3,
        Quarterly = 4
    }

    public enum RiskLevel
    {
        None = 0,
        Low = 1,
        Medium = 2,
        High = 3
    }

    public enum AuditMonth
    {
        NA = 0,
        January = 1,
        February = 2,
        March = 3,
        April = 4,
        May = 5,
        June = 6,
        July = 7,
        August = 8,
        September = 9,
        October = 10,
        November = 11,
        December = 12
    }

    public enum SpecCompliance
    {
        Low = 0,
        InSpec = 1,
        High = 2
    }
}

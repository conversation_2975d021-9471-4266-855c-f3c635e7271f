﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models
{
    [Table("Product", Schema = "Product")]
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [StringLength(100)]
        public string? ItemNumber { get; set; }

        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; } = string.Empty;

        [StringLength(255)]
        public string? Description { get; set; }

        [Column("GTIN")]
        [StringLength(14)]
        public string? Gtin { get; set; }

        [Column("l_ProductClassId")]
        public int? LProductClassId { get; set; }

        [Column("l_ProductCategoryId")]
        public int? LProductCategoryId { get; set; }

        [Column("l_ProductTypeId")]
        public int? LProductTypeId { get; set; }

        [Column("l_SaleUnitId")]
        public int? LSaleUnitId { get; set; }

        [Column(TypeName = "decimal(4, 2)")]
        public decimal Multiplier { get; set; }
        public bool IsBrandedProduct { get; set; }
        public bool IsProduct { get; set; }
        public DateTime ActivationDate { get; set; }
        public DateTime DeactivationDate { get; set; }
        public DateTime CreatedOn { get; set; }

        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public bool IsInactive { get; set; }
        public bool? IsMdfSpecialProduct { get; set; }
        public int? NewProductId { get; set; }

        public List<ProteinAuditSpec> ProteinAuditSpecs { get; set; } = new();
    }
}

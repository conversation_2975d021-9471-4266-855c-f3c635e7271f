﻿@page
@model EditModel
@using ProductCenter.Constants.Enums
@using ProductCenter.Models.ViewModels
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "Audit";
    var auditEvalList = Enum.GetValues<AuditEval>().Select(ef => new { Value = (int)ef, Text = ef.ToString() }).ToList();
}
<style>
    .AuditForm {
        max-width: 400px;
    }

    .SpecLow {
        border: solid 1px crimson;
    }

    .SpecIn {
        border: solid 1px darkgreen;
    }

    .SpecHigh {
        border: solid 1px gold;
    }
</style>
<div id="loadingDiv">Loading...</div>
<div id="formDiv" style="display:none;">
    <form method="post" onchange="onFormChange()" onsubmit="onFormSubmit()">
        @Html.HiddenFor(m => m.AuditVM.AuditId)
        @Html.HiddenFor(m => m.AuditVM.CreatedBy)
        @Html.HiddenFor(m => m.AuditVM.CreatedOn)
        @Html.HiddenFor(m => m.AuditVM.IsProteinAudit)
        <div>
            <span class="fw-bold">Scan Barcode: </span>
            <button type="button" class="btn btn-primary btn-sm" onclick="startDynamScan()"><span class="cameraIcon"></span>&nbsp;Camera</button>
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#scannerModal"><span class="barcodeScannerIcon"></span>&nbsp;Barcode Scanner</button>
        </div>
        <div class="row">
            <div class="col-lg-3">
                <h6 class="mt-2">Audit Information</h6>
                <hr />

                @(Html.Kendo().TextBoxFor(m => m.AuditVM.ItemNumber)
                    .Label(l => l.Content("Item Number"))
                    .HtmlAttributes(new { @readonly = true })
                    )
                <span asp-validation-for="AuditVM.ItemNumber" class="text-danger d-block"></span>
                @(Html.Kendo().DropDownListFor(m => m.AuditVM.ProductId)
                    .Label(l => l.Content("Product"))
                    .Filter(FilterType.Contains)
                    .OptionLabel("Select product...")
                    .DataValueField("Value")
                    .DataTextField("Text")
                    .Events(e => e.Cascade("OnProductChange"))
                    .DataSource(dataSource => dataSource
                    .Read(r => r.Url("/Audits/Edit?handler=ReadProductList").Data("forgeryToken"))
                    .ServerFiltering(false)
                    )
                    )
                <span asp-validation-for="AuditVM.ProductId" class="text-danger d-block"></span>

                @(Html.Kendo().DropDownListFor(m => m.AuditVM.SupplierId)
                    .Label(l => l.Content("Supplier"))
                    .OptionLabel("Select supplier...")
                    .DataValueField("Value")
                    .DataTextField("Text")
                    .CascadeFrom("AuditVM_ProductId")
                    .CascadeFromField("ProductId")
                    .CascadeFromParentField("Value")
                    .DataSource(dataSource => dataSource
                    .Read(r => r.Url("/Audits/Edit?handler=ReadSupplierList").Data("forgeryToken"))
                    .ServerFiltering(false)
                    )
                    )
                <span asp-validation-for="AuditVM.SupplierId" class="text-danger d-block"></span>

                @(Html.Kendo().DatePickerFor(m => m.AuditVM.Received)
                    .Label(l => l.Content("Received Date"))
                    )
                <span asp-validation-for="AuditVM.Received" class="text-danger d-block"></span>

                @(Html.Kendo().DatePickerFor(m => m.AuditVM.AuditDate)
                    .Label(l => l.Content("Audit Date"))
                    )
                <span asp-validation-for="AuditVM.AuditDate" class="text-danger d-block"></span>
            </div>
            <div class="col-lg-3">
                <h6 class="mt-2">Case Weight Check</h6>
                <hr />
                @(Html.Kendo().TextBoxFor(m => m.AuditVM.Weight)
                    .Label(l => l.Content("Weight"))
                    )
                <span asp-validation-for="AuditVM.Weight" class="text-danger d-block"></span>
                @* @(Html.Kendo().NumericTextBoxFor(m => m.AuditVM.Quantity)
                .Label(l => l.Content("Quantity"))
                .RestrictDecimals(true)
                .Decimals(0)
                .Min(0)
                .Step(1)
                .Format("#")
                )
                <span asp-validation-for="AuditVM.Quantity" class="text-danger d-block"></span> *@

            </div>
            <div class="col-lg-3">
                <h6 class="mt-2">Label Check Verification </h6>
                <hr class="" />
                @(Html.Kendo().TextBoxFor(m => m.AuditVM.GTIN)
                    .Label(l => l.Content("GTIN"))
                    )
                <span asp-validation-for="AuditVM.GTIN" class="text-danger d-block"></span>
                @(Html.Kendo().TextBoxFor(m => m.AuditVM.ProductionDate)
                    .Label(l => l.Content("Production Date"))
                    )
                <span asp-validation-for="AuditVM.ProductionDate" class="text-danger d-block"></span>
                @(Html.Kendo().TextBoxFor(m => m.AuditVM.SupplierCode)
                    .Label(l => l.Content("Supplier Code"))
                    )
                <span asp-validation-for="AuditVM.SupplierCode" class="text-danger d-block"></span>

                @(Html.Kendo().DropDownListFor(m => m.AuditVM.IngredientStatement)
                    .DataValueField("Value")
                    .Label(l => l.Content("Ingredient Statement"))
                    .DataTextField("Text")
                    .BindTo(auditEvalList)
                    )
                <span asp-validation-for="AuditVM.IngredientStatement" class="text-danger d-block"></span>
                @(Html.Kendo().DropDownListFor(m => m.AuditVM.Allergens)
                    .DataValueField("Value")
                    .Label(l => l.Content("Allergens"))
                    .DataTextField("Text")
                    .BindTo(auditEvalList)
                    )
                <span asp-validation-for="AuditVM.Allergens" class="text-danger d-block"></span>
                @(Html.Kendo().DropDownListFor(m => m.AuditVM.LotCodeCompliance)
                    .DataValueField("Value")
                    .Label(l => l.Content("GSI-121 Compliance"))
                    .DataTextField("Text")
                    .BindTo(auditEvalList)
                    )
                <span asp-validation-for="AuditVM.LotCodeCompliance" class="text-danger d-block"></span>

            </div>
            <div class="col-lg-3">
                <h6 class="mt-2">Quality Testing</h6>
                <hr />
                @(Html.Kendo().DropDownListFor(m => m.AuditVM.QualityAttributes)
                    .DataValueField("Value")
                    .Label(l => l.Content("Quality Attributes"))
                    .DataTextField("Text")
                    .BindTo(auditEvalList)
                    )
                <span asp-validation-for="AuditVM.QualityAttributes" class="text-danger d-block"></span>
                @(Html.Kendo().DropDownListFor(m => m.AuditVM.SensoryAttributes)
                    .DataValueField("Value")
                    .Label(l => l.Content("Sensory Attributes"))
                    .DataTextField("Text")
                    .BindTo(auditEvalList)
                    )
                <span asp-validation-for="AuditVM.SensoryAttributes" class="text-danger d-block"></span>
                @(Html.Kendo().TextAreaFor(m => m.AuditVM.Notes)
                    .Label(l => l.Content("Comments"))
                    .Rows(3)
                    )
                <span asp-validation-for="AuditVM.Notes" class="text-danger d-block"></span>
            </div>
        </div>
        <hr />
        <div id="ProteinAuditDiv" class=" mt-2" style="display:none;">
            <div class="row">
                <div id="bag0div" class="col">
                    <h5>Bag 1</h5>
                    <div class="row">
                        <div class="col-sm-1">
                            Piece
                        </div>
                        <div class="col">
                            Weight
                        </div>
                        <div class="col">
                            Length
                        </div>
                    </div>
                </div>
                <div id="bag1div" class="col">
                    <h5>Bag 2</h5>
                    <div class="row">
                        <div class="col-sm-1">
                            Piece
                        </div>
                        <div class="col">
                            Weight
                        </div>
                        <div class="col">
                            Length
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                @for (int i = 0; i < Model.AuditVM.ProteinAudits.Count; i++)
                {
                    <div class="col">
                        <div class="row">
                            <hr />
                            <div class="col">
                                @(Html.Kendo().NumericTextBoxFor(m => m.AuditVM.ProteinAudits[i].RemainingPiecesWeight)
                                    .Placeholder("Grams")
                                    .Label(l => l.Content("Weight of pieces left"))
                                    .Min(0)
                                    .Step(1)
                                    .Format("# g")
                                    )
                            </div>
                            <div class="col @(i < Model.AuditVM.ProteinAudits.Count -1 ? "border-end" : "")">
                                @(Html.Kendo().NumericTextBoxFor(m => m.AuditVM.ProteinAudits[i].TotalBagWeight)
                                    .Placeholder("Ounces")
                                    .Label(l => l.Content("Total bag weight"))
                                    .Min(0)
                                    .Step(.01m)
                                    .Format("#.00 oz")
                                    )
                            </div>
                        </div>
                    </div>
                }
            </div>
            <div class="row">
                <div class="col">
                    <div id="weightSpecChart"></div>
                </div>
                <div class="col">

                    <div id="lengthSpecChart"></div>
                </div>
            </div>
        </div>
        <div class="mt-2">
            <button class="btn btn-primary float-end" type="submit"><span class="saveIcon"></span>&nbsp;Save</button>
        </div>
    </form>
</div>

<!-- Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">Scan Barcode</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @(Html.Kendo().TextBox()
                    .Name("scannerData")
                    .Label(l => l.Content("Barcode"))
                    .HtmlAttributes(new { @readonly = true })
                    .Placeholder("Waiting for data...")
                    .Events(e => e.Change("OnScannerDataChange"))
                    )
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/icons.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-javascript-barcode@9.6.11/dist/dbr.js"></script>
    <partial name="_ValidationScriptsPartial" />
    <script type="text/javascript">
        function forgeryToken() {
            return kendo.antiForgeryTokens();
        }
    </script>

    <script>
        // Specifies a license, you can visit https://www.dynamsoft.com/customer/license/trialLicense?ver=9.6.11&utm_source=guide&product=dbr&package=js to get your own trial license good for 30 days.
        Dynamsoft.DBR.BarcodeScanner.license = 'DLS2eyJoYW5kc2hha2VDb2RlIjoiMTAxODM3Njk4LXIxNjgyMTA5Mjk2IiwibWFpblNlcnZlclVSTCI6Imh0dHBzOi8vbWx0cy5keW5hbXNvZnQuY29tLyIsIm9yZ2FuaXphdGlvbklEIjoiMTAxODM3Njk4Iiwic3RhbmRieVNlcnZlclVSTCI6Imh0dHBzOi8vc2x0cy5keW5hbXNvZnQuY29tLyIsImNoZWNrQ29kZSI6MTM4MjQ2OTc3OH0=';
        Dynamsoft.DBR.BarcodeReader.deviceFriendlyName = "ProductAudit";

        let scanner = null;
        // Initializes and uses the SDK
        (async () => {
            scanner = await Dynamsoft.DBR.BarcodeScanner.createInstance();
            scanner.onFrameRead = results => {
                if (results.length > 0) console.log(results);
            };
            scanner.onUniqueRead = (txt, result) => {
                console.log(result);
                let barcode = result.barcodeText;
                decode(barcode);
                scanner.hide();
            };

            scanner.setResolution(1920, 1080);

            scanner.setVideoFit('cover');

            // Obtain current runtime settings of `reader` instance.
            let settings = await scanner.getRuntimeSettings();
            // Specify the barcode formats by enumeration values.
            // There are two enumerations storing all supported barcode formats and each one needs to be set individually.
            // Use "|" to enable multiple barcode formats at one time.
            settings.barcodeFormatIds = Dynamsoft.DBR.EnumBarcodeFormat.BF_CODE_128;

            // Set the expected barcode count
            settings.expectedBarcodesCount = 1;

            settings.region.regionMeasuredByPercentage = 1;
            settings.region.regionLeft = 10;
            settings.region.regionTop = 40;
            settings.region.regionRight = 90;
            settings.region.regionBottom = 60;

            let uie = scanner.getUIElement();
            uie.style.zIndex = 1;
            // Update the settings.
            await scanner.updateRuntimeSettings(settings);

            let scanSettings = await scanner.getScanSettings();
            scanSettings.whenToPlaySoundforSuccessfulRead = "unique";
            await scanner.updateScanSettings(scanSettings);


        })();

        function startDynamScan() {
            scanner.show().then(function (e) {

            });

            document.getElementsByClassName("dce-btn-close")[0].classList.add("btn");
            document.getElementsByClassName("dce-btn-close")[0].classList.add("btn-primary");
            document.getElementsByClassName("dce-sel-camera")[0].classList.add("form-select");
            document.getElementsByClassName("dce-sel-resolution")[0].classList.add("form-select");
            document.getElementsByClassName("dce-btn-close")[0].innerHTML = `<span aria-hidden="true">&times;</span>`;
        }

        function decode(barcode) {
            $.ajax({
                type: "GET",
                url: "/Audits/Edit?handler=decode&barcode=" + barcode,
                headers: { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() },
                contentType: "application/json",
                dataType: "json",
                success: function (response) {
                    setScannedData(response);
                },
                failure: function (response) {
                    alert(response);
                },
                error: function (response) {
                    alert(response);
                }
            });
        }

        function setScannedData(scanData) {
            console.log(scanData);
            $("#AuditVM_ProductId").data("kendoDropDownList").value(scanData.ProductId);

            $("#AuditVM_SupplierId").data("kendoDropDownList").value(scanData.SupplierId);

            $("#AuditVM_ProductionDate").data("kendoTextBox").value(scanData.ProductionDate);

            $("#AuditVM_SupplierCode").data("kendoTextBox").value(scanData.LotCode);

            $("#AuditVM_GTIN").data("kendoTextBox").value(scanData.GTIN);
        }
    </script>
    <script>
        function OnProductChange(e) {
            let dataItem = e.sender.dataItem(e.sender.selectedIndex);
            $("#AuditVM_ItemNumber").data("kendoTextBox").value(dataItem.ItemNumber);

            if (dataItem.ProteinAudit) {
                $("#ProteinAuditDiv").show();
                $("#AuditVM_IsProteinAudit").val(true);
            }
            else {
                $("#ProteinAuditDiv").hide();
                $("#AuditVM_IsProteinAudit").val(false);
            }

            setCurrentProduct(dataItem);
        }

        $('#scannerModal').on('shown.bs.modal', function () {
            $('#scannerData').focus();
        })

        function OnScannerDataChange(e) {
            console.log(e);
        }

        let code = "";
        let reading = false;

        document.addEventListener('keypress', e => {
            //usually scanners throw an 'Enter' key at the end of read
            if (e.keyCode === 13) {
                e.preventDefault();
                if (code.length > 10) {
                    console.log(code);
                    $("#scannerData").data("kendoTextBox").value(code);
                    $('#scannerModal').modal('hide');
                    decode(code);
                    /// code ready to use
                    code = "";
                }
            } else {
                code += e.key; //while this is not an 'enter' it stores the every key
            }

            //run a timeout of 200ms at the first read and clear everything
            if (!reading) {
                reading = true;
                setTimeout(() => {
                    code = "";
                    reading = false;
                }, 200);  //200 works fine for me but you can adjust it
            }
        });
    </script>
    <script>
        let ProteinAudits = @(Json.Serialize(Model.AuditVM.ProteinAudits));
        let ErrorMessage = @(Json.Serialize(Model.ErrorMessage));
        let SaveSuccessful = @(Json.Serialize(Model.SaveSuccessful));
        let CurrentProduct;

        window.kendo.ui.progress($("body"), true);
        document.addEventListener("DOMContentLoaded", function () {
            if (!SaveSuccessful) {
                unsavedChanges = true;
            }

            if (ErrorMessage) {
                alert(ErrorMessage);
            }

            loadProteinAudit();
            window.kendo.ui.progress($("body"), false);
            $("#formDiv").show();
            $("#loadingDiv").hide();
        });

        let unsavedChanges = false;
        function onFormChange() {
            unsavedChanges = true;
        }
        function onFormSubmit() {
            unsavedChanges = false;
        }
        window.addEventListener('beforeunload', e => {
            if (unsavedChanges) {
                e.preventDefault()
                return e.returnValue = "Leave without saving changes?";
            }
        });

        function setCurrentProduct(item) {
            CurrentProduct = item;
            revalidateAll();
        }

        function loadProteinAudit() {
            ProteinAudits.forEach(function (audit, i) {
                audit.ProteinPieces.forEach(function (piece, p) {
                    addRow(i, piece.Grams, piece.Length);
                });
            });
        }

        let bagLength = [0, 0];
        function addRow(bag, grams = null, length = null) {
            let currentWeightInput = document.getElementById(`AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag] - 1}__Grams`);

            if (currentWeightInput && bag == 0 && bagLength[bag] > 0) {
                currentWeightInput.removeEventListener("input", addToBag1);
            }
            else if (currentWeightInput) {
                currentWeightInput.removeEventListener("input", addToBag2);
            }

            let bagDiv = document.getElementById(`bag${bag}div`);
            let newRow = document.createElement("div");
            newRow.classList.add("row", "mt-1");
            if (bag == 0) {
                newRow.classList.add("border-end");
            }

            let countCol = document.createElement("div");
            countCol.classList.add("col-1");
            countCol.innerHTML = bagLength[bag] + 1;
            newRow.appendChild(countCol);

            let weightDiv = document.createElement("div");
            weightDiv.classList.add("col");
            let weightInput = document.createElement("input");
            weightInput.classList.add("form-control");
            weightInput.id = `AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Grams`;
            weightInput.name = `AuditVM.ProteinAudits[${bag}].ProteinPieces[${bagLength[bag]}].Grams`;
            if (bag == 0) {
                weightInput.addEventListener("input", addToBag1);
            } else {
                weightInput.addEventListener("input", addToBag2);
            }
            weightInput.value = grams;
            weightDiv.appendChild(weightInput);

            let weightSpec = document.createElement("input");
            weightSpec.hidden = true;
            weightSpec.id = `AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__WeightSpecCompliance`;
            weightSpec.name = `AuditVM.ProteinAudits[${bag}].ProteinPieces[${bagLength[bag]}].WeightSpecCompliance`;
            weightDiv.appendChild(weightSpec);

            newRow.appendChild(weightDiv);

            let lengthDiv = document.createElement("div");
            lengthDiv.classList.add("col");
            let lengthInput = document.createElement("input");
            lengthInput.classList.add("form-control");
            lengthInput.id = `AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Length`;
            lengthInput.name = `AuditVM.ProteinAudits[${bag}].ProteinPieces[${bagLength[bag]}].Length`;
            lengthInput.value = length;
            lengthDiv.appendChild(lengthInput);

            let lengthSpec = document.createElement("input");
            lengthSpec.hidden = true;
            lengthSpec.id = `AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__LengthSpecCompliance`;
            lengthSpec.name = `AuditVM.ProteinAudits[${bag}].ProteinPieces[${bagLength[bag]}].LengthSpecCompliance`;
            lengthDiv.appendChild(lengthSpec);

            newRow.appendChild(lengthDiv);
            bagDiv.appendChild(newRow);

            $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Grams`).kendoNumericTextBox({
                format: "# g",
                min: 0,
                step: 1,
                placeholder: "Grams",
                size: "small",
                change: onWeightChange
            });
            $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Length`).kendoNumericTextBox({
                format: "#.00 in",
                min: 0,
                step: .25,
                restrictDecimals: true,
                placeholder: "Inches",
                size: "small",
                change: onLengthChange
            });
            let weightBox = $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Grams`).data("kendoNumericTextBox");
            revalidateAll();

            let lengthBox = $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${bagLength[bag]}__Length`).data("kendoNumericTextBox");
            revalidateAll();
            bagLength[bag]++;
        }

        function onLengthChange(e) {
            revalidateAll();
        }
        function onWeightChange(e) {
            revalidateAll();
        }

        function revalidateAll() {
            let lengthSpecResults = [];
            let weightSpecResults = [];

            for (let bag = 0; bag < bagLength.length; bag++) {
                for (let piece = 0; piece < bagLength[bag]; piece++) {
                    let weightBox = $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${piece}__Grams`).data("kendoNumericTextBox");
                    weightSpecResults.push(validateSpec(weightBox, 0));

                    let lengthBox = $(`#AuditVM_ProteinAudits_${bag}__ProteinPieces_${piece}__Length`).data("kendoNumericTextBox");
                    lengthSpecResults.push(validateSpec(lengthBox, 1));
                }
            }
            updateSummary(lengthSpecResults, 'lengthSpecChart');
            updateSummary(weightSpecResults, 'weightSpecChart');
        }

        function updateSummary(results, chartName) {
            let chartTitle = chartName == 'lengthSpecChart' ? 'Overall Average Length' : 'Overall Average Weight';
            let chartData = [
                { category: 'Under Spec', value: 0, color: 'crimson' },
                { category: 'In Spec', value: 0, color: 'darkgreen' },
                { category: 'Over Spec', value: 0, color: 'gold' }
            ];
            for (let i = 0; i < results.length; i++) {
                if (results[i] != null) chartData[results[i]].value++;
            }

            $(`#${chartName}`).kendoChart({
                title: {
                    text: chartTitle
                },
                transitions: false,
                legend: {
                    visible: false
                },
                seriesDefaults: {
                    labels: {
                        template: "#= category # - #= kendo.format('{0:P}', percentage)#",
                        position: "outsideEnd",
                        visible: true,
                        background: "transparent"
                    }
                },
                series: [{
                    type: "donut",
                    data: chartData
                }],
                tooltip: {
                    visible: true,
                    template: "#= category # - #= kendo.format('{0:P}', percentage) #"
                }
            });
        }

        function validateSpec(s, col) {
            let element = s.element[0];
            let wrapper = s.wrapper[0];
            let val = s.value();

            let MinSpec = col == 0 ? CurrentProduct?.ProteinAuditSpec?.MinWeight : CurrentProduct?.ProteinAuditSpec?.MinLength;
            let MaxSpec = col == 0 ? CurrentProduct?.ProteinAuditSpec?.MaxWeight : CurrentProduct?.ProteinAuditSpec?.MaxLength;
            let hiddenSpecId = wrapper.nextSibling.id;
            let hiddenSpec = document.getElementById(hiddenSpecId);

            if (val == null || val <= 0) {
                return;
            }
            else if (val < MinSpec) {
                wrapper.classList.add("SpecLow");
                wrapper.classList.remove("SpecHigh");
                wrapper.classList.remove("SpecIn");
                hiddenSpec.value = "0";
                return 0;
            }
            else if (val <= MaxSpec) {
                wrapper.classList.add("SpecIn");
                wrapper.classList.remove("SpecHigh");
                wrapper.classList.remove("SpecLow");
                hiddenSpec.value = "1";
                return 1;
            }
            else {
                wrapper.classList.add("SpecHigh");
                wrapper.classList.remove("SpecLow");
                wrapper.classList.remove("SpecIn");
                hiddenSpec.value = "2";
                return 2;
            }
        }

        function addToBag1() {
            addRow(0);
        }
        function addToBag2() {
            addRow(1);
        }

    </script>
}
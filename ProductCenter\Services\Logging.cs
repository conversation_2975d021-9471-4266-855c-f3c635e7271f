﻿using LogWriterCore;

namespace ProductCenter.Services
{
    public interface ILogging
    {
        Task LogMessage(string message, MessageType messageType, Exception? ex = null, Dictionary<string, string>? additionalData = null);
    }
    public class Logging : ILogging
    {
        private readonly IConfiguration _configuration;

        public Logging(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task LogMessage(string message, MessageType messageType, Exception? ex, Dictionary<string, string>? additionalData)
        {
            LogMessage logMessage = new LogMessage("ProductCenter", "1.0", ex);
            logMessage.Message = message;
            logMessage.MessageLevel = messageType;

            if (additionalData != null)
            {
                foreach (var data in additionalData)
                {
                    logMessage.AddAdditionalData(data.Key, data.Value);
                }
            }

            await LogWriter.WriteLog(logMessage);
        }
    }
}

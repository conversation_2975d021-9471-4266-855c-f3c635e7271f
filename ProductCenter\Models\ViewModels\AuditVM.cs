﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models.ViewModels
{
    public class AuditVM
    {
        public AuditVM() { }
        public AuditVM(Audit audit) 
        { 
            AuditId = audit.AuditId;
            ProductId = audit.ProductId;
            SupplierId = audit.SupplierId;
            //AuditType = audit.AuditType;
            Received = audit.Received;
            AuditDate = audit.AuditDate;
            //Quantity = audit.Quantity;
            Weight = audit.Weight;
            ProductionDate = audit.ProductionDate;
            SupplierCode = audit.SupplierCode;
            ItemNumber = audit.ItemNumber;
            GTIN = audit.GTIN;
            IngredientStatement = audit.IngredientStatement;
            Allergens = audit.Allergens;
            LotCodeCompliance = audit.LotCodeCompliance;
            QualityAttributes = audit.QualityAttributes;
            SensoryAttributes = audit.SensoryAttributes;
            Notes = audit.Notes;
            CreatedOn = audit.CreatedOn;
            CreatedBy = audit.CreatedBy;
            ModifiedOn = audit.ModifiedOn;
            ModifiedBy = audit.ModifiedBy;
            Product = audit.Product?.Name;
            Supplier = audit.Supplier?.Name;
            
            foreach(ProteinAudit proteinAudit in audit.ProteinAudits)
            {
                ProteinAudits.Add(new(proteinAudit));
            }
        }
        public int AuditId { get; set; }
        [Required(ErrorMessage = "Product is Required")]
        public int ProductId { get; set; }
        [Required(ErrorMessage = "Supplier is Required")]
        public int SupplierId { get; set; }
        //public AuditType AuditType { get; set; }

        [Required(ErrorMessage = "Received Date is Required")]
        public DateTime? Received { get; set; }
        [Required(ErrorMessage = "Audit Date is Required")]
        public DateTime? AuditDate { get; set; }
        public string ItemNumber { get; set; } = string.Empty;

        // Case Weight Check
        //[Required(ErrorMessage = "Quantity is Required")]
        //public int Quantity { get; set; }
        [Required(ErrorMessage = "Weight is Required")]
        public string Weight { get; set; } = string.Empty;

        // Label Check
        [Required(ErrorMessage = "Production Date is Required")]
        public string ProductionDate { get; set; } = string.Empty;
        [Required(ErrorMessage = "Supplier Code is Required")]
        public string SupplierCode { get; set; } = string.Empty;
        public string GTIN { get; set; } = string.Empty;

        public AuditEval IngredientStatement { get; set; }
        public AuditEval Allergens { get; set; }
        public AuditEval LotCodeCompliance { get; set; }


        // Quality Testing
        public AuditEval QualityAttributes { get; set; }
        public AuditEval SensoryAttributes { get; set; }

        public string? Notes { get; set; }

        public DateTime CreatedOn { get; set; }

        [Required]
        [StringLength(300)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(300)]
        public string? ModifiedBy { get; set; }


        public string? Product { get; set; }
        public string? Supplier { get; set; }

        public List<ProteinAuditVM> ProteinAudits { get; set; } = new();
        public bool IsProteinAudit { get; set; }
    }
}

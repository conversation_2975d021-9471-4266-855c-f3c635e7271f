using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using LogWriterCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using ProductCenter.Constants.Enums;
using ProductCenter.Data;
using ProductCenter.Models.ViewModels;
using ProductCenter.Services;

namespace ProductCenter.Pages.Audits
{
    public class HistoryModel(ApplicationDbContext context, ILogging logging) : PageModel
    {
        public void OnGet()
        {
        }

        public async Task<JsonResult> OnPostReadAuditsAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                var audits = context.Audit
                    .Include(p => p.Product)
                    .Include(p => p.Supplier)
                    .Select(s => new AuditVM(s));

                return new JsonResult(audits.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.History.OnPostReadAuditsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
        public async Task<JsonResult> OnPostDestroyAuditAsync([DataSourceRequest] DataSourceRequest request, AuditVM auditVM)
        {
            try
            {
                var audit = await context.Audit
                    .Where(a=>a.AuditId == auditVM.AuditId)
                    .FirstOrDefaultAsync();

                if (audit == null) return Constants.GridErrors.DestroyError;

                context.Audit.Remove(audit);
                await context.SaveChangesAsync();

                return new JsonResult(new[] { auditVM }.ToDataSourceResult(request, ModelState));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.History.OnPostReadAuditsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
    }
}

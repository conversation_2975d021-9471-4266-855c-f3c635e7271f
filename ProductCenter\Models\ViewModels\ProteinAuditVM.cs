﻿using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models.ViewModels
{
    public class ProteinAuditVM
    {
        public ProteinAuditVM() { }
        public ProteinAuditVM(ProteinAudit proteinAudit)
        {
            ProteinAuditId = proteinAudit.ProteinAuditId;
            TotalBagWeight = proteinAudit.TotalBagWeight;
            RemainingPiecesWeight = proteinAudit.RemainingPiecesWeight;
            
            foreach(var piece in proteinAudit.ProteinPieces)
            {
                ProteinPieces.Add(new(piece));
            }
        }

        public int ProteinAuditId { get; set; }

        public decimal? TotalBagWeight { get; set; }
        public int? RemainingPiecesWeight { get; set; }

        public List<ProteinPieceVM> ProteinPieces { get; set; } = new();
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class BagWeightToOz : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "TotalBagWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "TotalBagWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");
        }
    }
}

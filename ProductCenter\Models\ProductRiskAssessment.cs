﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models
{
    public class ProductRiskAssessment
    {
        public int ProductRiskAssessmentId { get; set; }
        public RiskLevel RiskLevel { get; set; }
        public AuditMonth AuditMonth { get; set; }
        public bool RequireProteinAudit { get; set; }
        public int ProductId { get; set; }

        public DateTime CreatedOn { get; set; }

        [Required]
        [StringLength(300)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(300)]
        public string? ModifiedBy { get; set; }

        public virtual Product? Product { get; set; }
    }
}

﻿using ProductCenter.Constants.Enums;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models.ViewModels
{
    public class ProductRiskAssessmentVM
    {
        public int ProductRiskAssessmentId { get; set; }
        [UIHint("_RiskLevel")]
        public RiskLevel RiskLevel { get; set; }
        [UIHint("_AuditMonthEditor")]
        public AuditMonth AuditMonth { get; set; }
        public bool RequireProteinAudit { get; set; }
        public int ProductId { get; set; }


        public DateTime CreatedOn { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }
        public string? ModifiedBy { get; set; }

        public string ItemNumber { get; set; } = string.Empty;
        public string Product { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime? LastAuditDate { get; set; }
        public int AuditQuarter { get; set; }
        public string? ProductionDate { get; set; }

        public AuditVM? LastAudit { get; set; }
        public int IsPastDue { get; set; }

        public ProteinAuditSpec? ProteinAuditSpec { get; set; }
    }
}

﻿@page
@model ProductCenter.Pages.Audits.RiskAssessmentModel
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "Risk Assessments";    
}

<div class="text-center">
    <h1 class="display-6">Risk Assessments</h1>
</div>
<div>
    @(
        Html.Kendo().Grid<Models.ViewModels.ProductRiskAssessmentVM>()
                .Name("riskAssessmentGrid")
                .Columns(columns =>
                {
                    columns.Bound(d=>d.ItemNumber).Filterable(true);
                    columns.Bound(d => d.Product).Filterable(ftb => ftb.Multi(true).Search(true));
                    columns.Bound(d => d.IsActive).Title("Active");
                    columns.Bound(d => d.RiskLevel).Filterable(ftb => ftb.Multi(true));
                    columns.Bound(d => d.AuditMonth).Filterable(ftb => ftb.Multi(true));
                    columns.Bound(d => d.RequireProteinAudit).Title("Protein Audit").Filterable(true);
                    columns.Bound(d => d.CreatedOn).Format("{0:d}").Hidden(); 
                    columns.Bound(d => d.CreatedBy).Hidden();
                    columns.Bound(d => d.ModifiedOn).Format("{0:d}").Hidden(); 
                    columns.Bound(d => d.ModifiedBy).Hidden();
                    columns.Command(command => { command.Edit(); command.Custom("Edit Spec").Click("OnEditSpecs").Visible("EditSpecsVisible"); });
                })
            .ToolBar(t => t.Search())
            .Search(s =>
            {
                s.Field(d => d.Product, "contains");
                s.Field(d => d.ItemNumber);
            })
            .Editable(editable => editable.Mode(GridEditMode.InLine))
            .Size(ComponentSize.Small)
            .Sortable() 
            .Scrollable(s=>s.Virtual(true))
            .Filterable()
            .Events(e=>e.BeforeEdit("onBeforeGridEdit").Edit("onGridEdit"))
            .DataSource(dataSource => dataSource
            .Ajax()
            .ServerOperation(false)
            .Sort(sort => { sort.Add("RiskLevel").Descending(); sort.Add("Product"); })
            .Read(r => r.Url("/Audits/RiskAssessment?handler=ReadRiskAssessments").Data("forgeryToken"))
            .Update(r => r.Url("/Audits/RiskAssessment?handler=UpdateRiskAssessment").Data("forgeryToken"))
            .Model(model =>
            {
                model.Id(d => d.ProductRiskAssessmentId);
                model.Field(f => f.ItemNumber).Editable(false);
                model.Field(f => f.Product).Editable(false);
                model.Field(f => f.RiskLevel).Editable(true);
                model.Field(f => f.AuditMonth).Editable(true);
                model.Field(f => f.RequireProteinAudit).Editable(true);
                model.Field(f => f.CreatedOn).Editable(false);
                model.Field(f => f.CreatedBy).Editable(false);
                model.Field(f => f.ModifiedOn).Editable(false);
                model.Field(f => f.ModifiedBy).Editable(false);
                model.Field(f => f.IsActive).Editable(false);
            })
        )
    )
</div>

<hr/>
<div class="text-center">
    <h1 class="display-6">Unevaluated Products</h1>
</div>
<div>
    @(
        Html.Kendo().Grid<Models.ViewModels.ProductVM>()
            .Name("unevaluatedGrid")
            .Columns(columns =>
            {
                columns.Bound(d => d.ItemNumber).Filterable(true);
                columns.Bound(d => d.Name).Filterable(ftb => ftb.Multi(true).Search(true));
                columns.Bound(d => d.RiskLevel).Filterable(ftb => ftb.Multi(true));
                columns.Bound(d => d.AuditMonth).Filterable(ftb => ftb.Multi(true));
                columns.Bound(d => d.RequireProteinAudit).Title("Protein Audit").Filterable(true);
                columns.Command(command => { command.Edit(); });
        })
        .Editable(editable => editable.Mode(GridEditMode.InLine))
        .Size(ComponentSize.Small)
        .Sortable()
        .Scrollable()
        .Filterable()
        .Events(e => e.BeforeEdit("onBeforeGridEdit").Edit("onGridEdit"))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(r => r.Url("/Audits/RiskAssessment?handler=ReadUnevaluated").Data("forgeryToken"))
        .Update(r => r.Url("/Audits/RiskAssessment?handler=CreateRiskAssessment").Data("forgeryToken"))
        .Events(e => e.RequestEnd("onRequestEnd"))
        .Model(model =>
        {
            model.Id(d => d.ProductId);
            model.Field(f => f.ItemNumber).Editable(false);
            model.Field(f => f.RiskLevel).Editable(true);
            model.Field(f => f.AuditMonth).Editable(true);
            model.Field(f => f.Name).Editable(false);
        })
        )
        )
</div>

<!-- Audit Spec Modal -->
<div class="modal fade" id="auditSpecModal" tabindex="-1" aria-labelledby="auditSpecModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditSpecModalLabel">Edit Protein Audit Spec</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="dniFileForm" method="post" asp-page="/Audits/RiskAssessment" asp-page-handler="UpdateAuditSpec">
                    @Html.HiddenFor(m=>m.ProteinAuditSpec.ProteinAuditSpecId)
                    @Html.HiddenFor(m=>m.ProteinAuditSpec.ProductId)
                    <div class="">
                        @(Html.Kendo().NumericTextBoxFor(m => m.ProteinAuditSpec.MinWeight)
                            .Label(l=>l.Content("Min Weight"))
                            .Min(0)
                            .Step(1)
                            .Placeholder("grams")
                            .Format("# g")
                            )
                        @(Html.Kendo().NumericTextBoxFor(m => m.ProteinAuditSpec.MaxWeight)
                            .Label(l=>l.Content("Max Weight"))
                            .Min(0)
                            .Step(1)
                            .Placeholder("grams")
                            .Format("# g")
                            )
                        @(Html.Kendo().NumericTextBoxFor(m => m.ProteinAuditSpec.MinLength)
                            .Label(l=>l.Content("Min Length"))
                            .Min(0)
                            .Step(.25m)
                            .Placeholder("inches")
                            .Format("#.00 in")
                            )
                        @(Html.Kendo().NumericTextBoxFor(m => m.ProteinAuditSpec.MaxLength)
                            .Label(l=>l.Content("Max Length"))
                            .Min(0)
                            .Step(.25m)
                            .Placeholder("inces")
                            .Format("#.00 in")
                            .RestrictDecimals(true)
                            )
                        <p style="padding-top: 1em; text-align: right">
                            <button type="submit" class="btn btn-primary">Submit</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">
        function forgeryToken() {
            return kendo.antiForgeryTokens();
        }

        function onRequestEnd(e) {
            if (e.type == 'update') {
                $('#riskAssessmentGrid').data('kendoGrid').dataSource.read();
                $('#riskAssessmentGrid').data('kendoGrid').refresh();

                $('#unevaluatedGrid').data('kendoGrid').dataSource.read();
                $('#unevaluatedGrid').data('kendoGrid').refresh();
            }
        }

        function onBeforeGridEdit(e) {
            var $riskAssessmentGrid = $("#riskAssessmentGrid").data("kendoGrid");
            $riskAssessmentGrid.cancelRow();
            var $unevaluatedGrid = $("#unevaluatedGrid").data("kendoGrid");
            $unevaluatedGrid.cancelRow();
        }

        function onGridEdit(e) {
            var $RiskLevel = $("#RiskLevel").data("kendoDropDownList");
            var $auditMonth = $("#AuditMonth").data("kendoDropDownList");
            var $proteinAudit = $("#RequireProteinAudit").kendoCheckBox().data("kendoCheckBox");

            console.log($proteinAudit);

            if ($RiskLevel.value() == @(Json.Serialize(Constants.Enums.RiskLevel.Low))) {
                $auditMonth.enable(true);
            }
            else {
                $auditMonth.enable(false);
                $auditMonth.value(0);                
            }

            if ($RiskLevel.value() == @(Json.Serialize(Constants.Enums.RiskLevel.High))) {
                $proteinAudit.enable(true);
            }
            else {
                $proteinAudit.enable(false);
            }
        }

        function EditSpecsVisible(e) {
            if (e.RequireProteinAudit) {
                return true;
            }
            return false;
        }

        function OnEditSpecs(e) {
            e.preventDefault();

            var dataItem = this.dataItem($(e.currentTarget).closest("tr"));
            console.log(dataItem);

            $("#ProteinAuditSpec_MinWeight").data("kendoNumericTextBox").value(dataItem?.ProteinAuditSpec?.MinWeight ?? 0);
            $("#ProteinAuditSpec_MaxWeight").data("kendoNumericTextBox").value(dataItem?.ProteinAuditSpec?.MaxWeight ?? 0);
            $("#ProteinAuditSpec_MinLength").data("kendoNumericTextBox").value(dataItem?.ProteinAuditSpec?.MinLength ?? 0);
            $("#ProteinAuditSpec_MaxLength").data("kendoNumericTextBox").value(dataItem?.ProteinAuditSpec?.MaxLength ?? 0);

            $("#ProteinAuditSpec_ProteinAuditSpecId").val(dataItem?.ProteinAuditSpec?.ProteinAuditSpecId ?? 0);
            $("#ProteinAuditSpec_ProductId").val(dataItem.ProductId);



            var auditSpecModal = new bootstrap.Modal(document.getElementById('auditSpecModal'));
            auditSpecModal.show();
        }
    </script>
}
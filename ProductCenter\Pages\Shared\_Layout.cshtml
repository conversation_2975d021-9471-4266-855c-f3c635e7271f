﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Product Center - @ViewData["Title"]</title>
    <link rel="icon" type="image/png" href="~/favicon.png">

    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ProductCenter.styles.css" asp-append-version="true" />

    @* Kendo *@
    <link rel="stylesheet" href="https://kendo.cdn.telerik.com/themes/7.2.0/bootstrap/bootstrap-main.css" />
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://unpkg.com/jszip/dist/jszip.min.js"></script>
    <script src="https://kendo.cdn.telerik.com/2024.2.514/js/kendo.all.min.js"></script>
    <script src="https://kendo.cdn.telerik.com/2024.2.514/js/kendo.aspnetmvc.min.js"></script>
    <script src="/js/kendo-ui-license.js"></script>
    @* Endo *@
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <img src="~/img/pfs128.png" width="30" height="30" class="d-inline-block align-top me-1" alt="">
                <a class="navbar-brand" asp-area="" asp-page="/Index">Product Center</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-page="/Index">Home</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link text-dark dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Audits
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <a class="nav-link text-dark dropdown-item" asp-area="" asp-page="/Audits/Index">Dashboard</a>
                                <li><hr class="dropdown-divider"></li>
                                <a class="nav-link text-dark dropdown-item" asp-area="" asp-page="/Audits/History">Audit History</a>
                                <li><hr class="dropdown-divider"></li>
                                <a class="nav-link text-dark dropdown-item" asp-area="" asp-page="/Audits/RiskAssessment">Risk Assessment</a>
                            </ul>
                        </li>
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    @* <script src="~/lib/jquery/dist/jquery.min.js"></script> *@
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
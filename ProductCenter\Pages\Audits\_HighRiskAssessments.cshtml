﻿<div class="text-center">
    <h1 class="display-6">High Risk Audits Due</h1>
</div>
<div>
    @(
        Html.Kendo().Grid<Models.ViewModels.ProductRiskAssessmentVM>()
        .Name("clearanceAuditGrid")
        .Columns(columns =>
        {
            columns.Bound(d => d.ItemNumber).Width(120).Media("(min-width: 450px)");
            columns.Bound(d => d.Product);
            columns.Bound(d => d.RequireProteinAudit).Title("Protein Audit");
            columns.Bound(d => d.LastAuditDate).Width(120).Format("{0:d}");
            columns.Bound(d=>d.ProductionDate);
            columns.Command(c => { c.Custom("View").Click("showDetails").IconClass("k-i-search").Visible("showDetailsButton"); c.Custom("Audit").IconClass("k-i-pencil").Click("onAudit"); });
        })
        .ToolBar(t => t.Search())
        .Search(s => s.Field(m => m.Product))
        .Size(ComponentSize.Small)
        .Sortable()
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(r => r.Url("/Audits/Index?handler=ReadClearanceAudits").Data("forgeryToken"))
        .Sort(s => { s.Add("LastAuditDate"); })
        )
        )
</div>
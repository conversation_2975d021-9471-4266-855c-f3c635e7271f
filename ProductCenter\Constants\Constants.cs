﻿using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;

namespace ProductCenter.Constants
{
    public static class GridErrors
    {
        public static JsonResult ReadError => new(new DataSourceResult { Errors = "Error reading records." });
        public static JsonResult CreateError => new(new DataSourceResult { Errors = "Error creating record." });
        public static JsonResult UpdateError => new(new DataSourceResult { Errors = "Error updating record." });
        public static JsonResult DestroyError => new(new DataSourceResult { Errors = "Error deleting record." });
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class AuditChanges : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Quantity",
                schema: "audit",
                table: "Audit");

            migrationBuilder.AlterColumn<decimal>(
                name: "TotalBagWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<int>(
                name: "RemainingPiecesWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "TotalBagWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "RemainingPiecesWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Quantity",
                schema: "audit",
                table: "Audit",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}

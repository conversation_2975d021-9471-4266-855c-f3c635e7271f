<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-ProductCenter-0a53204f-17fc-4fca-a82d-1e60f13c2ead</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LogWriter.Core" Version="1.0.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.4" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="2.18.1" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="2.18.1" />
    <PackageReference Include="Telerik.UI.for.AspNet.Core" Version="2024.2.514" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\js\kendo-ui-license.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>

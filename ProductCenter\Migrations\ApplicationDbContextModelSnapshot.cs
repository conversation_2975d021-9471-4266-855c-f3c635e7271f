﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ProductCenter.Data;

#nullable disable

namespace ProductCenter.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("audit")
                .HasAnnotation("ProductVersion", "8.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ProductCenter.Models.Audit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"));

                    b.Property<int>("Allergens")
                        .HasColumnType("int");

                    b.Property<DateTime?>("AuditDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("GTIN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IngredientStatement")
                        .HasColumnType("int");

                    b.Property<string>("ItemNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LotCodeCompliance")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("ProductionDate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("QualityAttributes")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Received")
                        .HasColumnType("datetime2");

                    b.Property<int>("SensoryAttributes")
                        .HasColumnType("int");

                    b.Property<string>("SupplierCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int");

                    b.Property<string>("Weight")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AuditId");

                    b.HasIndex("ProductId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Audit", "audit");
                });

            modelBuilder.Entity("ProductCenter.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"));

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DeactivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Gtin")
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)")
                        .HasColumnName("GTIN");

                    b.Property<bool>("IsBrandedProduct")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInactive")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsMdfSpecialProduct")
                        .HasColumnType("bit");

                    b.Property<bool>("IsProduct")
                        .HasColumnType("bit");

                    b.Property<string>("ItemNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("LProductCategoryId")
                        .HasColumnType("int")
                        .HasColumnName("l_ProductCategoryId");

                    b.Property<int?>("LProductClassId")
                        .HasColumnType("int")
                        .HasColumnName("l_ProductClassId");

                    b.Property<int?>("LProductTypeId")
                        .HasColumnType("int")
                        .HasColumnName("l_ProductTypeId");

                    b.Property<int?>("LSaleUnitId")
                        .HasColumnType("int")
                        .HasColumnName("l_SaleUnitId");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Multiplier")
                        .HasColumnType("decimal(4, 2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("NewProductId")
                        .HasColumnType("int");

                    b.HasKey("ProductId");

                    b.ToTable("Product", "Product", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("ProductCenter.Models.ProductRiskAssessment", b =>
                {
                    b.Property<int>("ProductRiskAssessmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductRiskAssessmentId"));

                    b.Property<int>("AuditMonth")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<bool>("RequireProteinAudit")
                        .HasColumnType("bit");

                    b.Property<int>("RiskLevel")
                        .HasColumnType("int");

                    b.HasKey("ProductRiskAssessmentId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductRiskAssessment", "audit");
                });

            modelBuilder.Entity("ProductCenter.Models.ProductSupplier", b =>
                {
                    b.Property<int>("ProductSupplierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductSupplierId"));

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DeactivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int");

                    b.HasKey("ProductSupplierId");

                    b.HasIndex("ProductId");

                    b.HasIndex("SupplierId");

                    b.ToTable("x_ProductToSupplier", "Product", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinAudit", b =>
                {
                    b.Property<int>("ProteinAuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProteinAuditId"));

                    b.Property<int>("AuditId")
                        .HasColumnType("int");

                    b.Property<int?>("RemainingPiecesWeight")
                        .HasColumnType("int");

                    b.Property<decimal?>("TotalBagWeight")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("ProteinAuditId");

                    b.HasIndex("AuditId");

                    b.ToTable("ProteinAudit", "audit");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinAuditSpec", b =>
                {
                    b.Property<int>("ProteinAuditSpecId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProteinAuditSpecId"));

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DeactivationDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("MaxLength")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("MaxWeight")
                        .HasColumnType("int");

                    b.Property<decimal>("MinLength")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("MinWeight")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("ProteinAuditSpecId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProteinAuditSpec", "audit");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinPiece", b =>
                {
                    b.Property<int>("ProteinPieceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProteinPieceId"));

                    b.Property<int?>("Grams")
                        .HasColumnType("int");

                    b.Property<decimal?>("Length")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LengthSpecCompliance")
                        .HasColumnType("int");

                    b.Property<int>("ProteinAuditId")
                        .HasColumnType("int");

                    b.Property<int?>("WeightSpecCompliance")
                        .HasColumnType("int");

                    b.HasKey("ProteinPieceId");

                    b.HasIndex("ProteinAuditId");

                    b.ToTable("ProteinPiece", "audit");
                });

            modelBuilder.Entity("ProductCenter.Models.Supplier", b =>
                {
                    b.Property<int>("SupplierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SupplierId"));

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DeactivationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SupplierId");

                    b.ToTable("Supplier", "Product", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("ProductCenter.Models.Audit", b =>
                {
                    b.HasOne("ProductCenter.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProductCenter.Models.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ProductCenter.Models.ProductRiskAssessment", b =>
                {
                    b.HasOne("ProductCenter.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("ProductCenter.Models.ProductSupplier", b =>
                {
                    b.HasOne("ProductCenter.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProductCenter.Models.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinAudit", b =>
                {
                    b.HasOne("ProductCenter.Models.Audit", "Audit")
                        .WithMany("ProteinAudits")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Audit");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinAuditSpec", b =>
                {
                    b.HasOne("ProductCenter.Models.Product", "Product")
                        .WithMany("ProteinAuditSpecs")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinPiece", b =>
                {
                    b.HasOne("ProductCenter.Models.ProteinAudit", "ProteinAudit")
                        .WithMany("ProteinPieces")
                        .HasForeignKey("ProteinAuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProteinAudit");
                });

            modelBuilder.Entity("ProductCenter.Models.Audit", b =>
                {
                    b.Navigation("ProteinAudits");
                });

            modelBuilder.Entity("ProductCenter.Models.Product", b =>
                {
                    b.Navigation("ProteinAuditSpecs");
                });

            modelBuilder.Entity("ProductCenter.Models.ProteinAudit", b =>
                {
                    b.Navigation("ProteinPieces");
                });
#pragma warning restore 612, 618
        }
    }
}

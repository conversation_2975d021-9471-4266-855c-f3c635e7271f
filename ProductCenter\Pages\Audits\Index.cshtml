﻿@page
@model ProductCenter.Pages.Audits.IndexModel
@using ProductCenter.Constants.Enums
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "Product Audit Dashboard";
}
<style>
    .completedAudit {
        background: MediumSeaGreen !important;
    }
    .pastDueAudit {
        background: orange !important;
    }
</style>
<div>
    <a class="btn btn-primary" asp-page="/Audits/Edit">New Audit</a>
</div>
<div class="mt-2">
    @(Html.Kendo().TabStrip()
        .Name("tabStrip")
        .Animation(animation =>
        animation.Open(effect =>
        effect.Fade(FadeDirection.In)))
        .Items(tabstrip =>
        {
            tabstrip.Add().Text("High").Selected(true).Content(@<partial name="_HighRiskAssessments"/>);
            tabstrip.Add().Text("Medium").Content(@<partial name="_MediumRiskAssessments" />);
            tabstrip.Add().Text("Low").Content(@<partial name="_LowRiskAssessments" />);
        })
        )
</div>


@(Html.Kendo().Window().Name("Details")
    .Title("Audit Details")
    .Visible(false)
    .Modal(true)
    .Scrollable(true)
    .Draggable(true)
    .Width(500)
    .Height(600)
)

@section Scripts {
    <script type="text/javascript">
        function forgeryToken() {
            return kendo.antiForgeryTokens();
        }

        function getButtons(data) {
            console.log(data);
            let buttonHtml = `<div class="d-grid gap-2">`;
            if (!data.LastAuditDate) {
                buttonHtml += `<button class="btn btn-primary">Audit</button>`;
            }
            else {
                buttonHtml += `<button class="btn btn-secondary" onclick="showDetails(${data.uid})">View</button>`;
            }

            buttonHtml += `</div>`
            return buttonHtml;
        }

        function showDetails(e) {
            e.preventDefault();

            var detailsTemplate = kendo.template($("#template").html());
            var dataItem = this.dataItem($(e.currentTarget).closest("tr"));
            var wnd = $("#Details").data("kendoWindow");

            wnd.content(detailsTemplate(dataItem));
            wnd.center().open();
        }
        var auditEvalList = @Json.Serialize(Enum.GetNames(typeof(AuditEval)));
        function getEnumValue(e) {
            return auditEvalList[e];
        }

        function showDetailsButton(e) {
            return e.LastAuditDate != null;
        }

        function showAuditButton(e) {
            return e.LastAuditDate == null;
        }

        function onAudit(e){
            e.preventDefault();

            var dataItem = this.dataItem($(e.currentTarget).closest("tr"));

            var url = `/Audits/Edit?productId=${dataItem.ProductId}`
            window.location.href = url;
        }

    </script>

    <script type="text/x-kendo-template" id="template">
        <div id="details-container">
            <h6>#= LastAudit.Product #</h6>
            <em>#= LastAudit.Supplier #</em>
            <hr/>
            <dl>
                <dt>Item Number: </dt><dl>#= LastAudit.ItemNumber #</dl>
                <dt>Received: </dt><dl>#= kendo.toString(LastAudit.Received, 'd') #</dl>
                <dt>Audit Date: </dt><dl>#= kendo.toString(LastAudit.AuditDate, 'd') #</dl>
                <dt>Weight: </dt><dl>#= LastAudit.Weight #</dl>
                <dt>Production Date: </dt><dl>#= LastAudit.ProductionDate #</dl>
                <dt>Supplier Code: </dt><dl>#= LastAudit.SupplierCode #</dl>
                <dt>GTIN: </dt><dl>#= LastAudit.GTIN #</dl>
                <dt>Ingredient Statement: </dt><dl>#= getEnumValue(LastAudit.IngredientStatement) #</dl>
                <dt>Allergens: </dt><dl>#= getEnumValue(LastAudit.Allergens) #</dl>
                <dt>GSI-121 Compliance: </dt><dl>#= getEnumValue(LastAudit.LotCodeCompliance) #</dl>
                <dt>Quality Attributes: </dt><dl>#= getEnumValue(LastAudit.QualityAttributes) #</dl>
                <dt>Sensory Attributes: </dt><dl>#= getEnumValue(LastAudit.SensoryAttributes) #</dl>
                <dt>Comments: </dt><dl>#= LastAudit.Notes #</dl>
            </dl>
        </div>
    </script>
}
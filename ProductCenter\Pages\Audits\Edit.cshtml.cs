using LogWriterCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using ProductCenter.Constants.Enums;
using ProductCenter.Data;
using ProductCenter.Models;
using ProductCenter.Models.ViewModels;
using ProductCenter.Services;
using Telerik.SvgIcons;

namespace ProductCenter.Pages.Audits
{
    public class EditModel(ApplicationDbContext context, ILogging logging) : PageModel
    {
		[BindProperty]
		public AuditVM AuditVM { get; set; } = new();

        public bool EnableAuditType { get; set; }
        public bool EnableProduct { get; set; }
        public string? ErrorMessage { get; set; }
        public bool SaveSuccessful { get; set; } = false;

        public async Task<IActionResult> OnGet(int id = 0, int productId = 0, bool? s = false)
        {
			try
			{
                if(id == 0)
                {
                    //AuditVM.AuditType = auditType ?? AuditType.Routine;
                    AuditVM.ProductId = productId;
                    AuditVM.AuditDate = DateTime.Now;
                    AuditVM.Received = DateTime.Now;
                    AuditVM.CreatedBy = User.Identity?.Name ??"";
                    AuditVM.CreatedOn = DateTime.UtcNow;
                    //EnableAuditType = auditType == null;
                    EnableProduct = productId == 0;

                    if(productId > 0)
                    {
                        var product = await context.Product.Where(p=>p.ProductId == productId).FirstOrDefaultAsync();
                        AuditVM.ItemNumber = product?.ItemNumber ?? "";
                    }
                }
                else
                {
                    var existing = await context.Audit
                        .Include(a=>a.ProteinAudits)
                            .ThenInclude(pa=>pa.ProteinPieces)
                        .Where(a=>a.AuditId == id)
                        .FirstOrDefaultAsync();
                    if (existing == null) return NotFound();
                    AuditVM = new(existing);
                }

                for(int i = AuditVM.ProteinAudits.Count; i < 2; i++)
                {
                    AuditVM.ProteinAudits.Add(new());
                }

                foreach(ProteinAuditVM proteinAudit in AuditVM.ProteinAudits)
                {                    
                    for(int p = proteinAudit.ProteinPieces.Count; p < 15; p++)
                    {
                        proteinAudit.ProteinPieces.Add(new());
                    }
                }

                if(s == true)
                {

                    ErrorMessage = "Saved";
                    SaveSuccessful = true;
                }

                return Page();
			}
			catch (Exception ex)
			{
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" },
                    { "id", id.ToString() },
                    { "productId", productId.ToString() }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Edit.OnGet' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return RedirectToPage("Error");
            }
        }

        public async Task<JsonResult> OnGetReadProductList()
        {
            try
            {
                var products = context.ProductRiskAssessment
                    .Include(p=>p.Product)
                        .ThenInclude(p=>p.ProteinAuditSpecs)
                    .Select(p => new { Text = p.Product.Name, Value = p.ProductId, ItemNumber = p.Product.ItemNumber, ProteinAudit = p.RequireProteinAudit, ProteinAuditSpec = p.Product.ProteinAuditSpecs.Where(p=>p.ActivationDate > p.DeactivationDate).FirstOrDefault() })
                    .OrderBy(p => p.Text);

                return new JsonResult(products);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Edit.OnGetReadProductList' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }

        public async Task<JsonResult> OnGetReadSupplierList()
        {
            try
            {
                var products = context.ProductSupplier
                    .Include(ps=>ps.Supplier)
                    .Select(p => new { Text = p.Supplier.Name, Value = p.SupplierId, ProductId = p.ProductId })
                    .OrderBy(p => p.Text);

                return new JsonResult(products);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Edit.OnGetReadSupplierList' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    ErrorMessage = "Invalid or missing input. Please ensure all required fields are entered.";
                    return Page();
                }

                Audit audit = new();
                if(AuditVM.AuditId != 0)
                {
                    var existing = await context.Audit
                        .Include(a => a.ProteinAudits)
                            .ThenInclude(pa => pa.ProteinPieces)
                        .Where(a => a.AuditId == AuditVM.AuditId)
                        .FirstOrDefaultAsync();
                    if (existing == null)
                    {
                        ErrorMessage = "Invalid Audit ID.";
                        return Page();
                    }

                    audit = existing;
                    audit.ModifiedBy = User.Identity?.Name ?? "";
                    audit.ModifiedOn = DateTime.UtcNow;
                }
                else
                {
                    audit.CreatedBy = User.Identity?.Name ?? "";
                    audit.CreatedOn = DateTime.UtcNow;
                }

                audit.ProductId = AuditVM.ProductId;
                audit.SupplierId = AuditVM.SupplierId;
                audit.Received = AuditVM.Received;
                audit.AuditDate = AuditVM.AuditDate;
                audit.Weight = AuditVM.Weight;
                //audit.Quantity = AuditVM.Quantity;
                audit.ProductionDate = AuditVM.ProductionDate;
                audit.SupplierCode = AuditVM.SupplierCode;
                audit.ItemNumber = AuditVM.ItemNumber;
                audit.GTIN = AuditVM.GTIN;
                audit.IngredientStatement = AuditVM.IngredientStatement;
                audit.Allergens = AuditVM.Allergens;
                audit.LotCodeCompliance = AuditVM.LotCodeCompliance;
                audit.QualityAttributes = AuditVM.QualityAttributes;
                audit.SensoryAttributes = AuditVM.SensoryAttributes;
                audit.Notes = AuditVM.Notes;

                // Remove All protein audits
                context.ProteinAudit.RemoveRange(audit.ProteinAudits);
                audit.ProteinAudits.Clear();

                foreach (ProteinAuditVM proteinAudit in AuditVM.ProteinAudits)
                {
                    ProteinAudit newProteinAudit = new() 
                    { 
                        RemainingPiecesWeight = proteinAudit.RemainingPiecesWeight,
                        TotalBagWeight = proteinAudit.TotalBagWeight
                    };

                    foreach(ProteinPieceVM piece in proteinAudit.ProteinPieces)
                    {
                        if(piece.Grams > 0 || piece.Length > 0)
                        {
                            newProteinAudit.ProteinPieces.Add(new() 
                            { 
                                Length = piece.Length, 
                                Grams = piece.Grams, 
                                LengthSpecCompliance = piece.LengthSpecCompliance, 
                                WeightSpecCompliance = piece.WeightSpecCompliance 
                            });
                        }
                    }
                    if(newProteinAudit.ProteinPieces.Count > 0)
                    {
                        audit.ProteinAudits.Add(newProteinAudit);
                    }
                }

                context.Update(audit);
                await context.SaveChangesAsync();

                return RedirectToPage("Edit", new {id = audit.AuditId, s = true });
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Edit.OnPostAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                ErrorMessage = "An error occured while saving. Please contact administrator.";
                return Page();
            }
        }

        public async Task<IActionResult> OnGetDecodeAsync(string barcode)
        {
            try
            {
                var parsedBarcode = ParseBarcode(barcode);
                var parsedLotCode = ParseLotCode(parsedBarcode);
                var parsedProductionDate = ParseProductionDate(parsedBarcode);
                var parsedGTIN = ParseGtin(parsedBarcode);

                var productId = await GetProductIdAsync(parsedGTIN);
                var supllierId = await GetSupplierIdAsync(parsedLotCode);

                BarcodeResultVM barcodeResultVM = new()
                {
                    LotCode = parsedLotCode,
                    ProductionDate = parsedProductionDate,
                    ProductId = productId,
                    SupplierId = supllierId,
                    GTIN = parsedGTIN
                };

                return new JsonResult(barcodeResultVM);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" },
                    { "barcode", barcode }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.Edit.OnGetDecodeAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return new JsonResult("Error");
            }
        }

        private static string ParseBarcode(string barcode)
        {
            try
            {
                var parsedBarcode = barcode.Replace("{GS}", "");
                parsedBarcode = parsedBarcode.Replace("(", "");
                parsedBarcode = parsedBarcode.Replace(")", "");
                return parsedBarcode;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
        private static string ParseLotCode(string Barcode)
        {
            try
            {
                if (Barcode.Length < 26) return string.Empty;
                return Barcode.Substring(26);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
        private static string ParseProductionDate(string Barcode)
        {
            try
            {
                if (Barcode.Length < 24) return string.Empty;
                return Barcode.Substring(18, 6);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
        private static string ParseGtin(string Barcode)
        {
            try
            {
                if (Barcode.Length < 16) return string.Empty;
                return Barcode.Substring(2, 14);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        private async Task<int> GetProductIdAsync(string gtin)
        {
            try
            {
                var product = await context.Product
                    .Where(p => p.Gtin == gtin)
                    .Where(p => p.ActivationDate > p.DeactivationDate)
                    .FirstOrDefaultAsync();
                return product?.ProductId ?? 0;
            }
            catch (Exception)
            {
                return 0;
            }
        }
        private async Task<int> GetSupplierIdAsync(string lotCode)
        {
            try
            {
                var sCode = lotCode.Length >= 3 ? lotCode.Substring(0,3) : string.Empty;
                var supplier = await context.Supplier
                    .Where(p => p.SupplierCode == sCode)
                    .Where(p => p.ActivationDate > p.DeactivationDate)
                    .FirstOrDefaultAsync();
                return supplier?.SupplierId ?? 0;
            }
            catch (Exception)
            {
                return 0;
            }
        }
    }
}

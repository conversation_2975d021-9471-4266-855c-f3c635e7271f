﻿@{
    int currentQtr =  (DateTime.UtcNow.Month - 1) / 3 + 1;
}
<div class="text-center mt-2">
    <h1 class="display-6">Medium Risk Audits Due</h1>
</div>
<div>
    @(
        Html.Kendo().Grid<Models.ViewModels.ProductRiskAssessmentVM>()
        .Name("quarterlyAuditGrid")
        .Columns(columns =>
        {
            columns.Bound(d => d.AuditQuarter).Title("Quarter").Filterable(ftb => ftb.Multi(true));
            columns.Bound(d => d.ItemNumber).Width(120).Media("(min-width: 450px)").Filterable(false);
            columns.Bound(d => d.Product).Filterable(false);
            columns.Bound(d => d.LastAuditDate).Width(120).Title("Audit Date").Format("{0:d}").Filterable(false);
            columns.Bound(d => d.ProductionDate).Filterable(false);
            columns.Command(c => { c.Custom("View").Click("showDetails").IconClass("k-i-search").Visible("showDetailsButton"); c.Custom("Audit").Click("onAudit").IconClass("k-i-pencil").Visible("showAuditButton"); });
        })
        .ToolBar(t => t.Search())
        .Search(s => s.Field(m => m.Product))
        .Size(ComponentSize.Small)
        .Filterable()
        .Events(e => e.DataBound("onQuarterlyDataBound"))
        .DataSource(dataSource => dataSource
        .Ajax()
        .ServerOperation(false)
        .Read(r => r.Url("/Audits/Index?handler=ReadQuarterlyAudits").Data("forgeryToken"))
        .Sort(s => { s.Add("AuditQuarter"); s.Add("LastAuditDate"); })
        .Filter(filters =>
        {
            filters.Add(a => a.AuditQuarter).IsEqualTo(currentQtr);
        })
        )
        )
</div>

<script>
    function onQuarterlyDataBound(e) {
        var items = e.sender.items();

        var firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);

        items.each(function (index) {
            var dataItem = $("#quarterlyAuditGrid").data("kendoGrid").dataItem(this);
            if (dataItem.LastAuditDate >= firstDayOfYear) {
                this.className += " completedAudit";
            }
        });
    }
</script>
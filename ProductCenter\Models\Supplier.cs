﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace ProductCenter.Models
{
    public class Supplier
    {
        public int SupplierId { get; set; }
        public string SupplierNumber { get; set; } = string.Empty;
        public string? Name { get; set; } = string.Empty;
        public DateTime ActivationDate { get; set; }
        public DateTime DeactivationDate { get; set; }
        public DateTime CreatedOn { get; set; }

        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedOn { get; set; }

        [StringLength(50)]
        public string? ModifiedBy { get; set; }

        public string? SupplierCode { get; set; } = string.Empty;
    }
}

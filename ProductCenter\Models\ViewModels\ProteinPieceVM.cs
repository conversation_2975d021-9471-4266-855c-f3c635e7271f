﻿using ProductCenter.Constants.Enums;

namespace ProductCenter.Models.ViewModels
{
    public class ProteinPieceVM
    {
        public ProteinPieceVM() { }
        public ProteinPieceVM(ProteinPiece proteinPiece) 
        {
            ProteinPieceId = proteinPiece.ProteinPieceId;
            Grams = proteinPiece.Grams;
            Length = proteinPiece.Length;
            ProteinAuditId = proteinPiece.ProteinAuditId;
        }

        public int ProteinPieceId { get; set; }
        public int? Grams { get; set; }
        public SpecCompliance? WeightSpecCompliance { get; set; }
        public decimal? Length { get; set; }
        public SpecCompliance? LengthSpecCompliance { get; set; }
        public int ProteinAuditId { get; set; }
        public ProteinAudit? ProteinAudit { get; set; }
    }
}

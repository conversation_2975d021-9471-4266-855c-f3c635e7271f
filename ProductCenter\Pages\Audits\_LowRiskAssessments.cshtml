﻿@using ProductCenter.Constants.Enums
@{
    AuditMonth currentMonth = (AuditMonth)DateTime.Now.Month;
}
<script>
</script>
<div class="text-center mt-2">
    <h1 class="display-6">Low Risk Audits Due</h1>
</div>
<div>
    @(
        Html.Kendo().Grid<Models.ViewModels.ProductRiskAssessmentVM>()
                    .Name("routineAuditGrid")
                    .Columns(columns =>
                    {
                        columns.Bound(d => d.ItemNumber).Width(120).Media("(min-width: 450px)").Filterable(false);
                        columns.Bound(d => d.Product).Filterable(false);
                        columns.Bound(d => d.AuditMonth).Filterable(ftb => ftb.Multi(true));
                        columns.Bound(d => d.LastAuditDate).Title("Audit Date").Width(120).Format("{0:d}").Filterable(false);
                        columns.Bound(d => d.ProductionDate).Filterable(false);
                        columns.Bound(d => d.IsPastDue).Hidden();
                        columns.Command(c => { c.Custom("View").Click("showDetails").IconClass("k-i-search").Visible("showDetailsButton"); c.Custom("Audit").Click("onAudit").IconClass("k-i-pencil").Visible("showAuditButton"); });
                    })
                .ToolBar(t => t.Search())
                .Search(s => s.Field(m => m.Product))
                .Filterable()
                .Size(ComponentSize.Small)
                .Events(e => e.DataBound("onRoutineDataBound"))
                .DataSource(dataSource => dataSource
                .Ajax()
                .ServerOperation(false)
                .Read(r => r.Url("/Audits/Index?handler=ReadAnnualAudits").Data("forgeryToken"))
                .Sort(s => { s.Add("AuditMonth"); s.Add("LastAuditDate");})
                .Filter(filters =>
                {
                    filters.Add(a => a.AuditMonth).IsEqualTo(currentMonth);                    
                })
        )
        )
</div>

<script>
    function onRoutineDataBound(e) {
        var firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);

        var items = e.sender.items();
        items.each(function (index) {
            var dataItem = $("#routineAuditGrid").data("kendoGrid").dataItem(this);
            if (dataItem.LastAuditDate >= firstDayOfYear) {
                this.className += " completedAudit";
            }
            if (dataItem.IsPastDue) {
                this.className += " pastDueAudit";
            }
        });
    }    
</script>
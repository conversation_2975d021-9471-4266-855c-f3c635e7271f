﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProductCenter.Migrations
{
    /// <inheritdoc />
    public partial class pieceweight : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WeightLeft",
                schema: "audit",
                table: "ProteinAudit");

            migrationBuilder.RenameColumn(
                name: "PiecesLeft",
                schema: "audit",
                table: "ProteinAudit",
                newName: "TotalBagWeight");

            migrationBuilder.AlterColumn<decimal>(
                name: "Length",
                schema: "audit",
                table: "ProteinPiece",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<int>(
                name: "Grams",
                schema: "audit",
                table: "ProteinPiece",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "RemainingPiecesWeight",
                schema: "audit",
                table: "ProteinAudit",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RemainingPiecesWeight",
                schema: "audit",
                table: "ProteinAudit");

            migrationBuilder.RenameColumn(
                name: "TotalBagWeight",
                schema: "audit",
                table: "ProteinAudit",
                newName: "PiecesLeft");

            migrationBuilder.AlterColumn<decimal>(
                name: "Length",
                schema: "audit",
                table: "ProteinPiece",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Grams",
                schema: "audit",
                table: "ProteinPiece",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WeightLeft",
                schema: "audit",
                table: "ProteinAudit",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }
    }
}

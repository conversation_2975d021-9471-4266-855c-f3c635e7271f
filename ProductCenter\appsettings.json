{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "champschicken.com", "TenantId": "4fcdf1bc-df6d-453a-bf65-18ef6f42af0d", "ClientId": "524f015d-f154-4bdf-942d-28e4954b78f3", "CallbackPath": "/signin-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DbConn": "Server=tcp:alpaca-sql.database.windows.net,1433;Initial Catalog=Hackberry-db;Persist Security Info=False;User ID=AuditUser;Password=****************$!;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}}
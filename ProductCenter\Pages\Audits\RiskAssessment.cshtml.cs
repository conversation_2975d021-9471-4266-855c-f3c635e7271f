using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using LogWriterCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using ProductCenter.Constants.Enums;
using ProductCenter.Data;
using ProductCenter.Models;
using ProductCenter.Models.ViewModels;
using ProductCenter.Services;

namespace ProductCenter.Pages.Audits
{
    public class RiskAssessmentModel(ApplicationDbContext context, ILogging logging) : PageModel
    {
        [BindProperty]
        public ProteinAuditSpec ProteinAuditSpec { get; set; } = new();

        public void OnGet()
        {
        }


        public async Task<JsonResult> OnPostReadRiskAssessmentsAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                var riskAssessments = context.ProductRiskAssessment
                    .Include(p=>p.Product)
                    .Select(s => new ProductRiskAssessmentVM
                    {
                        ProductRiskAssessmentId = s.ProductRiskAssessmentId,
                        Product = s.Product.Name,
                        ProductId = s.ProductId,
                        ItemNumber = s.Product.ItemNumber,
                        RiskLevel = s.RiskLevel,
                        AuditMonth = s.AuditMonth,
                        RequireProteinAudit = s.RequireProteinAudit,
                        CreatedBy = s.CreatedBy,
                        CreatedOn = s.CreatedOn,
                        ModifiedBy = s.ModifiedBy,
                        ModifiedOn = s.ModifiedOn,
                        IsActive = s.Product.IsInactive == false && s.Product.ActivationDate > s.Product.DeactivationDate,
                        ProteinAuditSpec = s.Product.ProteinAuditSpecs.Where(p=>p.ActivationDate>p.DeactivationDate).FirstOrDefault()
                    });

                return new JsonResult(riskAssessments.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostReadRiskAssessmentsAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
        public async Task<JsonResult> OnPostReadUnevaluatedAsync([DataSourceRequest] DataSourceRequest request)
        {
            try
            {
                var productRiskAssessments = await context.ProductRiskAssessment.Select(r => r.ProductId).ToListAsync();
                var products = await context.Product
                    .Where(p => p.IsProduct == true && p.IsInactive == false && p.ActivationDate > p.DeactivationDate)
                    .Where(p => !productRiskAssessments.Any(r => r == p.ProductId))
                    .Select(p => new ProductVM { ProductId = p.ProductId, Name = p.Name, ItemNumber = p.ItemNumber})
                    .ToListAsync();

                return new JsonResult(products.ToDataSourceResult(request));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostReadUnevaluatedAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.ReadError;
            }
        }
        public async Task<JsonResult> OnPostCreateRiskAssessmentAsync([DataSourceRequest] DataSourceRequest request, ProductVM productVM)
        {
            try
            {
                ProductRiskAssessment newRiskAssessment = new()
                {
                    ProductId = productVM.ProductId,
                    RiskLevel = productVM.RiskLevel,
                    AuditMonth = productVM.AuditMonth,
                    RequireProteinAudit = productVM.RequireProteinAudit,
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = User.Identity?.Name ?? ""
                };

                await context.ProductRiskAssessment.AddAsync(newRiskAssessment);
                await context.SaveChangesAsync();

                return new JsonResult(new[] { productVM }.ToDataSourceResult(request, ModelState));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostCreateRiskAssessmentAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.CreateError;
            }
        }
        public async Task<JsonResult> OnPostUpdateRiskAssessmentAsync([DataSourceRequest] DataSourceRequest request, ProductRiskAssessmentVM productRiskAssessmentVM)
        {
            try
            {
                var existing = await context.ProductRiskAssessment
                    .Where(p => p.ProductRiskAssessmentId == productRiskAssessmentVM.ProductRiskAssessmentId)
                    .FirstOrDefaultAsync();

                if(existing == null) return Constants.GridErrors.UpdateError;

                existing.RiskLevel = productRiskAssessmentVM.RiskLevel;
                existing.AuditMonth = productRiskAssessmentVM.AuditMonth;
                existing.RequireProteinAudit = productRiskAssessmentVM.RequireProteinAudit;
                existing.ModifiedOn = DateTime.UtcNow;
                existing.ModifiedBy = User.Identity?.Name ?? "";

                await context.SaveChangesAsync();

                return new JsonResult(new[] { productRiskAssessmentVM }.ToDataSourceResult(request, ModelState));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostCreateRiskAssessmentAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.CreateError;
            }
        }
        public async Task<JsonResult> OnPostDestroyRiskAssessmentAsync([DataSourceRequest] DataSourceRequest request, ProductRiskAssessmentVM productRiskAssessmentVM)
        {
            try
            {
                var existing = await context.ProductRiskAssessment
                    .Where(p => p.ProductRiskAssessmentId == productRiskAssessmentVM.ProductRiskAssessmentId)
                    .FirstOrDefaultAsync();
                if (existing == null) { return Constants.GridErrors.DestroyError; }

                context.ProductRiskAssessment.Remove(existing);
                await context.SaveChangesAsync();

                return new JsonResult(new[] { productRiskAssessmentVM }.ToDataSourceResult(request, ModelState));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostDestroyRiskAssessmentAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return Constants.GridErrors.CreateError;
            }
        }

        public async Task<JsonResult> OnGetReadProductList()
        {
            try
            {
                var productRiskAssessments = await context.ProductRiskAssessment.Select(r=>r.ProductId).ToListAsync();
                var products = context.Product
                    .Where(p => p.IsProduct == true && p.IsInactive == false && p.ActivationDate > p.DeactivationDate)
                    .Where(p=> !productRiskAssessments.Any(r=>r == p.ProductId))
                    .Select(p => new { Text = p.Name, Value = p.ProductId })
                    .OrderBy(p => p.Text)
                    .ToListAsync();


                return new JsonResult(products);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnGetReadProductList' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return new JsonResult("");
            }
        }

        public async Task<JsonResult> OnGetReadSupplierList()
        {
            try
            {
                var products = context.ProductSupplier
                    .Include(ps => ps.Supplier)
                    .Select(p => new { Text = p.Supplier.Name, Value = p.SupplierId, ProductId = p.ProductId })
                    .OrderBy(p => p.Text);

                return new JsonResult(products);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnGetReadSupplierList' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return new JsonResult("");
            }
        }

        public async Task<IActionResult> OnPostUpdateAuditSpecAsync()
        {
            try
            {
                // Check for existing
                var existingSpec = await context.ProteinAuditSpec.Where(p=>p.ProteinAuditSpecId == ProteinAuditSpec.ProteinAuditSpecId).FirstOrDefaultAsync();

                // If record exists, deactivate it
                if(existingSpec != null)
                {
                    existingSpec.DeactivationDate = DateTime.Now;
                    existingSpec.ModifiedOn = DateTime.Now;
                    existingSpec.ModifiedBy = User.Identity?.Name ?? "User Not Found";
                }

                // Create new Record
                ProteinAuditSpec newSpec = new()
                {
                    ActivationDate = DateTime.Now,
                    DeactivationDate = DateTime.MinValue,
                    CreatedOn = DateTime.Now,
                    CreatedBy = User.Identity?.Name ?? "User Not Found",
                    ProductId = ProteinAuditSpec.ProductId,
                    MinWeight = ProteinAuditSpec.MinWeight,
                    MaxWeight = ProteinAuditSpec.MaxWeight,
                    MinLength = ProteinAuditSpec.MinLength,
                    MaxLength = ProteinAuditSpec.MaxLength
                };

                await context.AddAsync(newSpec);

                await context.SaveChangesAsync();
                return RedirectToPage("RiskAssessment");
            }
            catch (Exception ex)
            {
                Dictionary<string, string> additionalData = new Dictionary<string, string>
                {
                    { "user", User.Identity?.Name ?? "User Not Found" }
                };

                _ = logging.LogMessage("Error occurred in 'Audits.RiskAssessment.OnPostUpdateAuditSpecAsync' method.", MessageType.Error, ex, additionalData).ConfigureAwait(false);
                return RedirectToPage("Error");
            }
        }

    }    
}
